jQuery(document).ready(function($) {
    // Helper function to add CSRF header to Q-Updater AJAX requests only
    function addCSRFToQUpdaterAjax(ajaxOptions) {
        if (typeof qUpdater !== 'undefined' && qUpdater.csrf_header) {
            ajaxOptions.beforeSend = function(xhr) {
                xhr.setRequestHeader(qUpdater.csrf_header, qUpdater.nonce);
            };
        }
        return ajaxOptions;
    }


    // Handle auto-update toggle in plugins list
    $(document).on('click', '.q-updater-auto-update-toggle', function(e) {
        e.preventDefault();

        // Get the plugin slug from the data attribute
        const pluginSlug = $(this).data('plugin');

        // Redirect to Q-Updater settings page with update-settings tab and highlight the plugin
        window.location.href = qUpdater.settings_url + '#update-settings';

        // Store the plugin to highlight in sessionStorage
        if (pluginSlug) {
            sessionStorage.setItem('qu_highlight_plugin', pluginSlug);
        }
    });
    // Enhanced Settings page navigation with performance optimization
    $('.qu-nav-tab-js').on('click', function(e) {
        e.preventDefault();

        const $tab = $(this);
        const tabId = $tab.data('tab');

        if (tabId) {
            // Use the enhanced tab switcher if available
            if (typeof QuTabSwitcher !== 'undefined') {
                QuTabSwitcher.switchToTab(tabId);
                QuTabSwitcher.updateURL(tabId);
            } else {
                // Fallback to basic tab switching
                basicTabSwitch(tabId, $tab);
            }
        }
    });

    // Basic tab switching fallback
    function basicTabSwitch(tabId, $tab) {
        // Update active class
        $('.qu-nav-tab').removeClass('qu-nav-tab-active').attr('aria-current', 'false');
        $tab.addClass('qu-nav-tab-active').attr('aria-current', 'page');

        // Hide all panels and show the target panel
        $('.qu-tab-panel').removeClass('qu-tab-panel-active').hide();
        $('#' + tabId + '-tab').addClass('qu-tab-panel-active').fadeIn(300);

        // Update URL hash without scrolling
        if (history.pushState) {
            history.pushState(null, null, '#' + tabId);
        } else {
            location.hash = tabId;
        }
    }

    // Legacy settings navigation (for backward compatibility)
    $('.qu-settings-nav-item').on('click', function(e) {
        e.preventDefault();

        const targetId = $(this).attr('href');

        // Update active class
        $('.qu-settings-nav-item').removeClass('active');
        $(this).addClass('active');

        // Hide all sections and show the target section
        $('.qu-settings-section').hide();
        $(targetId).fadeIn(300);

        // Update URL hash without scrolling
        if (history.pushState) {
            history.pushState(null, null, targetId);
        } else {
            location.hash = targetId;
        }
    });

    // Tools page navigation
    $('.qu-tools-nav-item').on('click', function(e) {
        e.preventDefault();

        const targetId = $(this).attr('href');

        // Update active class
        $('.qu-tools-nav-item').removeClass('active');
        $(this).addClass('active');

        // Hide all sections and show the target section
        $('.qu-tools-content').hide();
        $(targetId).fadeIn(300);
    });

    // Plugin Manager page navigation
    $('.qu-plugin-manager-nav-item').on('click', function(e) {
        e.preventDefault();

        const targetId = $(this).attr('href');

        // Update active class
        $('.qu-plugin-manager-nav-item').removeClass('active');
        $(this).addClass('active');

        // Hide all sections and show the target section with animation
        $('.qu-plugin-manager-content').hide();
        $(targetId).fadeIn(300).addClass('qu-fade-in');
    });

    // Handle navigation buttons within plugin manager
    $(document).on('click', '.qu-nav-button', function(e) {
        e.preventDefault();

        const targetId = $(this).attr('href');

        // Trigger click on the corresponding nav item
        $(`.qu-plugin-manager-nav-item[href="${targetId}"]`).trigger('click');
    });

    // Plugin Manager buttons with enhanced UI feedback
    $('#check-all-updates').on('click', function(e) {
        e.preventDefault();

        const $button = $(this);
        const $statusMessage = $('#manual-check-status');

        // Show loading state
        $button.prop('disabled', true);
        $button.find('.dashicons').addClass('qu-spin');
        $statusMessage.html('<span class="dashicons dashicons-update qu-spin"></span> Checking for updates...').show();

        // Trigger the existing functionality with callback
        $('#manual-check-q-updates').trigger('click');

        // Monitor the status message for changes
        const checkStatusInterval = setInterval(function() {
            if (!$('#manual-check-q-updates').prop('disabled')) {
                clearInterval(checkStatusInterval);
                $button.prop('disabled', false);
                $button.find('.dashicons').removeClass('qu-spin');

                // Add fade-out after a delay
                setTimeout(function() {
                    $statusMessage.fadeOut(500);
                }, 5000);
            }
        }, 500);
    });

    $('#run-all-updates').on('click', function(e) {
        e.preventDefault();

        if (!confirm('Are you sure you want to update all plugins? This may take a few moments.')) {
            return;
        }

        const $button = $(this);
        const $statusMessage = $('#manual-check-status');

        // Show loading state
        $button.prop('disabled', true);
        $button.find('.dashicons').addClass('qu-spin');
        $statusMessage.html('<span class="dashicons dashicons-update qu-spin"></span> Updating all plugins...').show();

        // Trigger the existing functionality
        $('#run-auto-updates').trigger('click');

        // Monitor the status message for changes
        const checkStatusInterval = setInterval(function() {
            if (!$('#run-auto-updates').prop('disabled')) {
                clearInterval(checkStatusInterval);
                $button.prop('disabled', false);
                $button.find('.dashicons').removeClass('qu-spin');
            }
        }, 500);
    });

    // Advanced Tools buttons
    $('#clear-plugin-cache').on('click', function(e) {
        e.preventDefault();

        const $button = $(this);
        const $status = $('#advanced-tools-status');

        $button.prop('disabled', true);
        $status.html('<div class="qu-loading-indicator"><span class="dashicons dashicons-update-alt qu-spin"></span> Clearing plugin cache...</div>');

        // Simulate cache clearing (in a real implementation, this would be an AJAX call)
        setTimeout(function() {
            $status.html('<div class="qu-success-state"><span class="dashicons dashicons-yes"></span> Plugin cache cleared successfully!</div>');
            $button.prop('disabled', false);
        }, 1500);
    });

    $('#run-plugin-diagnostics').on('click', function(e) {
        e.preventDefault();

        const $button = $(this);
        const $status = $('#advanced-tools-status');

        $button.prop('disabled', true);
        $status.html('<div class="qu-loading-indicator"><span class="dashicons dashicons-update-alt qu-spin"></span> Running diagnostics...</div>');

        // Simulate diagnostics (in a real implementation, this would be an AJAX call)
        setTimeout(function() {
            $status.html(`
                <div class="qu-success-state">
                    <span class="dashicons dashicons-yes"></span> Diagnostics completed!
                </div>
                <div style="margin-top: 15px;">
                    <h4>Results:</h4>
                    <ul>
                        <li><span class="dashicons dashicons-yes" style="color: green;"></span> Plugin file permissions: OK</li>
                        <li><span class="dashicons dashicons-yes" style="color: green;"></span> GitHub API connection: OK</li>
                        <li><span class="dashicons dashicons-yes" style="color: green;"></span> WordPress update system: OK</li>
                    </ul>
                </div>
            `);
            $button.prop('disabled', false);
        }, 2000);
    });

    // Reviews functionality
    // Toggle write review form
    $('.qu-write-review-button').on('click', function(e) {
        e.preventDefault();
        $('#write-review').slideToggle(300);

        // If opening the form and we have a plugin selected in the filter, pre-select it
        if ($('#write-review').is(':visible')) {
            const selectedPlugin = $('#plugin-filter').val();
            if (selectedPlugin) {
                $('#review-plugin').val(selectedPlugin);
            }

            // Scroll to the form
            $('html, body').animate({
                scrollTop: $('#write-review').offset().top - 50
            }, 500);
        }
    });

    // Cancel review button
    $('.qu-cancel-review').on('click', function(e) {
        e.preventDefault();
        $('#write-review').slideUp(300);

        // Reset form
        $('#qu-review-form')[0].reset();
        $('.qu-star-rating-input .qu-star').removeClass('qu-star-filled');
        $('.qu-star-rating-input .qu-star .dashicons').removeClass('dashicons-star-filled').addClass('dashicons-star-empty');
        $('#review-rating').val(0);
    });

    // Star rating functionality
    $('.qu-star-rating-input .qu-star').on('click', function() {
        const rating = $(this).data('rating');
        $('#review-rating').val(rating);

        // Update stars visual state
        $('.qu-star-rating-input .qu-star').each(function() {
            const starRating = $(this).data('rating');
            if (starRating <= rating) {
                $(this).addClass('qu-star-filled');
                $(this).find('.dashicons').removeClass('dashicons-star-empty').addClass('dashicons-star-filled');
            } else {
                $(this).removeClass('qu-star-filled');
                $(this).find('.dashicons').removeClass('dashicons-star-filled').addClass('dashicons-star-empty');
            }
        });
    });

    // Handle review form submission
    $('#qu-review-form').on('submit', function(e) {
        e.preventDefault();

        const $form = $(this);
        const $submitButton = $form.find('button[type="submit"]');
        const pluginSlug = $('#review-plugin').val();
        const rating = $('#review-rating').val();
        const reviewText = $('#review-text').val();

        // Validate form
        if (!pluginSlug) {
            alert('Please select a plugin to review.');
            return;
        }

        if (rating < 1) {
            alert('Please select a rating (1-5 stars).');
            return;
        }

        if (!reviewText.trim()) {
            alert('Please enter your review text.');
            return;
        }

        // Disable submit button and show loading state
        $submitButton.prop('disabled', true).html('<span class="dashicons dashicons-update qu-spin"></span> Submitting...');

        // Submit review via AJAX
        $.ajax(addCSRFToQUpdaterAjax({
            url: qUpdater.ajaxurl,
            type: 'POST',
            data: {
                action: 'qu_submit_review',
                nonce: qUpdater.review_submit_nonce,
                plugin_slug: pluginSlug,
                rating: rating,
                review_text: reviewText
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    alert('Review submitted successfully!');

                    // Reset form and hide it
                    $form[0].reset();
                    $('.qu-star-rating-input .qu-star').removeClass('qu-star-filled');
                    $('.qu-star-rating-input .qu-star .dashicons').removeClass('dashicons-star-filled').addClass('dashicons-star-empty');
                    $('#review-rating').val(0);
                    $('#write-review').slideUp(300);

                    // Reload page to show the new review
                    location.reload();
                } else {
                    alert('Error: ' + (response.data.message || 'Failed to submit review.'));
                    $submitButton.prop('disabled', false).html('<span class="dashicons dashicons-saved"></span> Submit Review');
                }
            },
            error: function() {
                alert('An error occurred while submitting your review. Please try again.');
                $submitButton.prop('disabled', false).html('<span class="dashicons dashicons-saved"></span> Submit Review');
            }
        }));
    });

    // Handle review deletion
    $(document).on('click', '.qu-delete-review', function(e) {
        e.preventDefault();

        if (!confirm('Are you sure you want to delete this review? This action cannot be undone.')) {
            return;
        }

        const $button = $(this);
        const reviewId = $button.data('review-id');
        const $reviewItem = $button.closest('.qu-review-item');

        $button.prop('disabled', true).html('<span class="dashicons dashicons-update qu-spin"></span>');

        $.ajax(addCSRFToQUpdaterAjax({
            url: qUpdater.ajaxurl,
            type: 'POST',
            data: {
                action: 'qu_delete_review',
                nonce: qUpdater.review_delete_nonce,
                review_id: reviewId
            },
            success: function(response) {
                if (response.success) {
                    // Fade out and remove the review item
                    $reviewItem.fadeOut(300, function() {
                        $(this).remove();

                        // If no reviews left, reload to show empty state
                        if ($('.qu-review-item').length === 0) {
                            location.reload();
                        }
                    });
                } else {
                    alert('Error: ' + (response.data.message || 'Failed to delete review.'));
                    $button.prop('disabled', false).html('<span class="dashicons dashicons-trash"></span> Delete');
                }
            },
            error: function() {
                alert('An error occurred while deleting the review. Please try again.');
                $button.prop('disabled', false).html('<span class="dashicons dashicons-trash"></span> Delete');
            }
        }));
    });

    // Enhanced hash handling for new tab system
    function handleHash() {
        const hash = window.location.hash.replace('#', '');

        // Check if it's a new tab system hash
        if (hash && $('#' + hash + '-tab').length) {
            // Use enhanced tab switcher if available
            if (typeof QuTabSwitcher !== 'undefined') {
                QuTabSwitcher.switchToTab(hash);
            } else {
                // Fallback to basic tab switching
                $('.qu-nav-tab').removeClass('qu-nav-tab-active').attr('aria-current', 'false');
                $(`.qu-nav-tab[data-tab="${hash}"]`).addClass('qu-nav-tab-active').attr('aria-current', 'page');
                $('.qu-tab-panel').removeClass('qu-tab-panel-active').hide();
                $('#' + hash + '-tab').addClass('qu-tab-panel-active').show();
            }
        }
        // Legacy hash handling
        else if (hash && $('#' + hash).length && $('#' + hash).hasClass('qu-settings-section')) {
            $('.qu-settings-nav-item').removeClass('active');
            $(`.qu-settings-nav-item[href="#${hash}"]`).addClass('active');
            $('.qu-settings-section').hide();
            $('#' + hash).show();

            // Check if we need to highlight a plugin for auto-update
            if (hash === '#update-settings') {
                const pluginToHighlight = sessionStorage.getItem('qu_highlight_plugin');
                if (pluginToHighlight) {
                    // Highlight the plugin checkbox
                    const $checkbox = $(`#auto_update_${pluginToHighlight}`);
                    if ($checkbox.length) {
                        // Add a highlight effect to the plugin item
                        const $pluginItem = $checkbox.closest('.qu-plugin-auto-update-item');
                        $pluginItem.addClass('qu-highlight-plugin');

                        // Scroll to the plugin item
                        setTimeout(function() {
                            $('html, body').animate({
                                scrollTop: $pluginItem.offset().top - 100
                            }, 500);

                            // Remove the highlight after a few seconds
                            setTimeout(function() {
                                $pluginItem.removeClass('qu-highlight-plugin');
                            }, 3000);
                        }, 500);

                        // Clear the stored plugin
                        sessionStorage.removeItem('qu_highlight_plugin');
                    }
                }
            }
        } else if ($('#general-settings').length) {
            // Default to general settings if no valid hash
            $('.qu-settings-section').hide();
            $('#general-settings').show();
        }
    }

    // Handle hash on page load
    handleHash();

    // Handle hash changes - with safety check
    if (typeof window !== 'undefined' && window !== null) {
        $(window).on('hashchange', handleHash);
    }

    // Dropdown menu accessibility
    $('.qu-dropdown-toggle').on('keydown', function(e) {
        // If Enter or Space is pressed
        if (e.keyCode === 13 || e.keyCode === 32) {
            e.preventDefault();
            $(this).next('.qu-dropdown-menu').toggle();
            $(this).attr('aria-expanded', $(this).next('.qu-dropdown-menu').is(':visible'));
        }
    });

    // Close dropdown when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.qu-dropdown').length) {
            $('.qu-dropdown-menu').hide();
            $('.qu-dropdown-toggle').attr('aria-expanded', 'false');
        }
    });

    // Enhanced tooltips with keyboard accessibility
    // Convert static tooltips to interactive ones
    $('.qu-settings-tooltip').each(function() {
        const $tooltip = $(this);

        // Make tooltips focusable and keyboard accessible
        $tooltip.attr({
            'tabindex': '0',
            'role': 'button',
            'aria-label': 'Help information',
            'aria-expanded': 'false'
        });

        // Add ARIA support
        $tooltip.find('.qu-tooltip-content').attr({
            'role': 'tooltip',
            'id': 'tooltip-' + Math.floor(Math.random() * 10000)
        });

        $tooltip.attr('aria-describedby', $tooltip.find('.qu-tooltip-content').attr('id'));
    });

    // Show tooltip on focus as well as hover
    $('.qu-settings-tooltip').on('focus', function() {
        $(this).attr('aria-expanded', 'true');
    }).on('blur', function() {
        $(this).attr('aria-expanded', 'false');
    });

    // Handle keyboard activation of tooltips
    $('.qu-settings-tooltip').on('keydown', function(e) {
        // If Enter or Space is pressed
        if (e.keyCode === 13 || e.keyCode === 32) {
            e.preventDefault();
            $(this).toggleClass('qu-tooltip-active');
            $(this).attr('aria-expanded', $(this).hasClass('qu-tooltip-active'));
        }
        // If Escape is pressed
        if (e.keyCode === 27) {
            e.preventDefault();
            $(this).removeClass('qu-tooltip-active');
            $(this).attr('aria-expanded', 'false');
        }
    });

    // Show settings saved message with animation
    if ($('.notice-success').length) {
        $('.notice-success').hide().slideDown(300).delay(3000).slideUp(300);
    }

    // Handle dismissible notices
    $(document).on('click', '.notice.is-dismissible .notice-dismiss', function() {
        const $notice = $(this).closest('.notice');
        const noticeType = $notice.data('notice');

        if (!noticeType) return;

        $.ajax(addCSRFToQUpdaterAjax({
            url: qUpdater.ajaxurl,
            type: 'POST',
            data: {
                action: 'q_updater_dismiss_notice',
                nonce: qUpdater.dismiss_nonce,
                notice_type: noticeType
            }
        }));

        // Animate the notice dismissal
        $notice.slideUp(300, function() {
            $(this).remove();
        });
    });
    $('#bulk-update-q-plugins').on('click', function(e) {
        e.preventDefault();

        const $button = $(this);
        const $status = $('#bulk-update-status');

        $button.prop('disabled', true);
        $status.html('<p>Updating plugins...</p>');

        $.ajax(addCSRFToQUpdaterAjax({
            url: qUpdater.ajaxurl,
            type: 'POST',
            data: {
                action: 'bulk_update_q_plugins',
                nonce: qUpdater.bulk_update_nonce
            },
            success: function(response) {
                if (response.success) {
                    const results = response.data;
                    let message = '<p>Update complete!</p>';

                    if (results.success.length > 0) {
                        message += `<p>Successfully updated ${results.success.length} plugin(s).</p>`;
                    }

                    if (results.failed.length > 0) {
                        message += `<p>Failed to update ${results.failed.length} plugin(s).</p>`;
                    }

                    $status.html(message);
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    $status.html('<p>Error: ' + response.data + '</p>');
                    $button.prop('disabled', false);
                }
            },
            error: function() {
                $status.html('<p>An error occurred during the update process.</p>');
                $button.prop('disabled', false);
            }
        }));
    });

    $('#run-auto-updates').on('click', function(e) {
        e.preventDefault();

        if (!confirm('Are you sure you want to run auto-updates now? This will update all enabled plugins to their latest versions.')) {
            return;
        }

        const $button = $(this);
        const $status = $('#manual-check-status');

        $button.prop('disabled', true);
        $status.html('<p>Running auto-updates...</p>');

        $.ajax(addCSRFToQUpdaterAjax({
            url: qUpdater.ajaxurl,
            type: 'POST',
            data: {
                action: 'run_auto_updates',
                nonce: qUpdater.bulk_update_nonce
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;
                    let message = `<p>${data.message}</p>`;

                    if (data.updated && data.updated.length > 0) {
                        message += '<p>Updated plugins:</p><ul>';
                        data.updated.forEach(function(plugin) {
                            message += `<li>${plugin.slug}: ${plugin.old_version} → ${plugin.new_version}</li>`;
                        });
                        message += '</ul>';
                    }

                    if (data.failed && data.failed.length > 0) {
                        message += '<p>Failed updates:</p><ul>';
                        data.failed.forEach(function(plugin) {
                            message += `<li>${plugin.slug}: ${plugin.reason}</li>`;
                        });
                        message += '</ul>';
                    }

                    $status.html(message);

                    if (data.updated && data.updated.length > 0) {
                        setTimeout(function() {
                            location.reload();
                        }, 3000);
                    }
                } else {
                    $status.html('<p>Error: ' + response.data + '</p>');
                }
                $button.prop('disabled', false);
            },
            error: function() {
                $status.html('<p>An error occurred while running auto-updates.</p>');
                $button.prop('disabled', false);
            }
        }));
    });

    $('#manual-check-q-updates').on('click', function(e) {
        e.preventDefault();

        const $button = $(this);
        const $status = $('#manual-check-status');

        $button.prop('disabled', true);
        $status.html('<p>Checking for updates...</p>');

        $.ajax(addCSRFToQUpdaterAjax({
            url: qUpdater.ajaxurl,
            type: 'POST',
            data: {
                action: 'manual_check_q_updates',
                nonce: qUpdater.bulk_update_nonce
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;
                    let message = `<p>Checked ${data.total_checked} Q plugins.</p>`;

                    // Display any errors first
                    if (data.errors && data.errors.length > 0) {
                        message += '<div class="notice notice-warning"><p>' +
                            'Some updates could not be checked:</p><ul>';
                        data.errors.forEach(function(error) {
                            message += `<li>${error}</li>`;
                        });
                        message += '</ul></div>';
                    }

                    if (data.updates_available > 0) {
                        message += `<p>Found ${data.updates_available} plugin(s) with available updates:</p><ul>`;
                        data.plugins.forEach(function(plugin) {
                            if (plugin.has_update) {
                                message += `<li>${plugin.name}: ${plugin.current_version} → ${plugin.latest_version}</li>`;
                            }
                        });
                        message += '</ul>';
                    } else {
                        message += '<p>All plugins are up to date!</p>';
                    }

                    $status.html(message);

                    if (data.updates_available > 0) {
                        setTimeout(function() {
                            location.reload();
                        }, 3000);
                    }
                } else {
                    $status.html('<p>Error: ' + response.data + '</p>');
                }
                $button.prop('disabled', false);
            },
            error: function() {
                $status.html('<p>An error occurred while checking for updates.</p>');
                $button.prop('disabled', false);
            }
        }));
    });

    $('#rollback-plugin-select').on('change', function() {
        const $select = $(this);
        const $container = $('#rollback-versions-container');
        const plugin = $select.val();
        const repo = $select.find('option:selected').data('repo');
        const currentVersion = $select.find('option:selected').data('current');

        if (!plugin) {
            $container.empty();
            return;
        }

        $container.html('<div class="qu-loading-indicator"><span class="dashicons dashicons-update-alt qu-spin"></span> Loading available versions...</div>');

        $.ajax(addCSRFToQUpdaterAjax({
            url: qUpdater.ajaxurl,
            type: 'POST',
            data: {
                action: 'get_plugin_releases',
                nonce: qUpdater.bulk_update_nonce,
                repo: repo
            },
            success: function(response) {
                if (response.success && response.data.length > 0) {
                    let html = '';

                    response.data.forEach(function(release) {
                        const releaseDate = new Date(release.published_at).toLocaleDateString();
                        const isCurrentVersion = release.version === currentVersion;

                        html += `<div class="qu-rollback-version-item">
                            <div class="qu-version-info">
                                <div class="qu-version-number">${release.version} ${isCurrentVersion ? '<span class="qu-version-current">Current</span>' : ''}</div>
                                <div class="qu-version-date">Released on ${releaseDate}</div>
                            </div>`;

                        if (!isCurrentVersion && release.download_url) {
                            html += `<button class="qu-button qu-rollback-button" data-plugin="${plugin}"
                                data-version="${release.version}" data-url="${release.download_url}">
                                <span class="dashicons dashicons-backup"></span> Rollback</button>`;
                        }

                        html += '</div>';
                    });

                    $container.html(html);
                } else {
                    $container.html('<div class="qu-empty-state"><span class="dashicons dashicons-info"></span> No releases found for this plugin.</div>');
                }
            },
            error: function() {
                $container.html('<div class="qu-error-state"><span class="dashicons dashicons-warning"></span> Error loading version information.</div>');
            }
        }));
    });

    $(document).on('click', '.qu-rollback-button', function(e) {
        e.preventDefault();

        if (!confirm('Warning: Rolling back may cause compatibility issues. Backup your site first. Continue?')) {
            return;
        }

        const $button = $(this);
        const plugin = $button.data('plugin');
        const version = $button.data('version');
        const downloadUrl = $button.data('url');

        $button.prop('disabled', true).text('Rolling back...');

        $.ajax(addCSRFToQUpdaterAjax({
            url: qUpdater.ajaxurl,
            type: 'POST',
            data: {
                action: 'rollback_plugin',
                nonce: qUpdater.bulk_update_nonce,
                plugin: plugin,
                version: version,
                download_url: downloadUrl
            },
            success: function(response) {
                if (response.success) {
                    alert(response.data);
                    location.reload();
                } else {
                    alert('Error: ' + response.data);
                    $button.prop('disabled', false).text('Rollback');
                }
            },
            error: function(xhr) {
                alert('Rollback failed: ' + (xhr.responseJSON?.data || 'Connection error'));
                $button.prop('disabled', false).text('Rollback');
            }
        }));
    });

    // Handle backup plugin select
    $('#backup-plugin-select').on('change', function() {
        const $select = $(this);
        const $container = $('#plugin-backups-container');
        const plugin = $select.val();

        if (!plugin) {
            $container.html('<div class="qu-empty-state"><span class="dashicons dashicons-archive"></span><p>Select a plugin to view available backups</p></div>');
            return;
        }

        $container.html('<div class="qu-loading-indicator"><span class="dashicons dashicons-update-alt qu-spin"></span> Loading available backups...</div>');

        // Get the backups for this plugin
        $.ajax(addCSRFToQUpdaterAjax({
            url: qUpdater.ajaxurl,
            type: 'POST',
            data: {
                action: 'get_plugin_backups',
                nonce: qUpdater.bulk_update_nonce,
                plugin: plugin
            },
            success: function(response) {
                if (response.success && response.data.length > 0) {
                    let html = '';

                    response.data.forEach(function(backup) {
                        const backupDate = new Date(backup.date).toLocaleString();
                        const fileSize = formatBytes(backup.size);

                        html += `<div class="qu-backup-item">
                            <div class="qu-backup-info">
                                <div class="qu-backup-version">Version ${backup.version}</div>
                                <div class="qu-backup-date">Created on ${backupDate}</div>
                                <div class="qu-backup-size">Size: ${fileSize}</div>
                            </div>
                            <button class="qu-button qu-restore-button" data-plugin="${plugin}" data-backup="${backup.file}">
                                <span class="dashicons dashicons-image-rotate"></span> Restore
                            </button>
                        </div>`;
                    });

                    $container.html(html);
                } else {
                    $container.html('<div class="qu-empty-state"><span class="dashicons dashicons-info"></span> No backups found for this plugin.</div>');
                }
            },
            error: function() {
                $container.html('<div class="qu-error-state"><span class="dashicons dashicons-warning"></span> Error loading backup information.</div>');
            }
        }));
    });

    // Handle restore from backup button
    $(document).on('click', '.qu-restore-button', function(e) {
        e.preventDefault();

        if (!confirm('Warning: Restoring from a backup will replace the current version. Continue?')) {
            return;
        }

        const $button = $(this);
        const plugin = $button.data('plugin');
        const backupFile = $button.data('backup');

        $button.prop('disabled', true).html('<span class="dashicons dashicons-update qu-spin"></span> Restoring...');

        $.ajax(addCSRFToQUpdaterAjax({
            url: qUpdater.ajaxurl,
            type: 'POST',
            data: {
                action: 'restore_plugin_from_backup',
                nonce: qUpdater.bulk_update_nonce,
                plugin: plugin,
                backup_file: backupFile
            },
            success: function(response) {
                if (response.success) {
                    alert(response.data);
                    location.reload();
                } else {
                    alert('Error: ' + response.data);
                    $button.prop('disabled', false).html('<span class="dashicons dashicons-image-rotate"></span> Restore');
                }
            },
            error: function(xhr) {
                alert('Restoration failed: ' + (xhr.responseJSON?.data || 'Connection error'));
                $button.prop('disabled', false).html('<span class="dashicons dashicons-image-rotate"></span> Restore');
            }
        }));
    });

    // Helper function to format bytes
    function formatBytes(bytes, decimals = 2) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const dm = decimals < 0 ? 0 : decimals;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    }

    // Handle single plugin update button
    $(document).on('click', '.qu-update-plugin', function(e) {
        e.preventDefault();

        if (!confirm('Are you sure you want to update this plugin? It\'s recommended to backup your site first.')) {
            return;
        }

        const $button = $(this);
        const plugin = $button.data('plugin');
        const version = $button.data('version');

        // Add loading state
        $button.prop('disabled', true).html('<span class="dashicons dashicons-update qu-spin"></span> Updating...');

        $.ajax(addCSRFToQUpdaterAjax({
            url: qUpdater.ajaxurl,
            type: 'POST',
            data: {
                action: 'update_single_plugin',
                nonce: qUpdater.bulk_update_nonce,
                plugin: plugin,
                version: version
            },
            success: function(response) {
                if (response.success) {
                    alert(response.data);
                    location.reload();
                } else {
                    alert('Error: ' + response.data);
                    $button.prop('disabled', false).text('Update Now');
                }
            },
            error: function(xhr) {
                alert('Update failed: ' + (xhr.responseJSON?.data || 'Connection error'));
                $button.prop('disabled', false).text('Update Now');
            }
        }));
    });

    // Handle plugin uninstall button
    $(document).on('click', '.qu-uninstall-plugin', function(e) {
        e.preventDefault();

        if (!confirm('WARNING: Are you sure you want to uninstall this plugin? This action cannot be undone.')) {
            return;
        }

        const $button = $(this);
        const plugin = $button.data('plugin');

        // Add loading state
        $button.prop('disabled', true).html('<span class="dashicons dashicons-trash qu-spin"></span> Uninstalling...');

        $.ajax(addCSRFToQUpdaterAjax({
            url: qUpdater.ajaxurl,
            type: 'POST',
            data: {
                action: 'uninstall_plugin',
                nonce: qUpdater.bulk_update_nonce,
                plugin: plugin
            },
            success: function(response) {
                if (response.success) {
                    alert(response.data);
                    location.reload();
                } else {
                    alert('Error: ' + response.data);
                    $button.prop('disabled', false).html('<span class="dashicons dashicons-trash"></span> Uninstall');
                }
            },
            error: function(xhr) {
                alert('Uninstall failed: ' + (xhr.responseJSON?.data || 'Connection error'));
                $button.prop('disabled', false).html('<span class="dashicons dashicons-trash"></span> Uninstall');
            }
        }));
    });

    $('#install-github-plugin').on('click', function(e) {
        e.preventDefault();

        const $button = $(this);
        const $status = $('#install-plugin-status');
        const repo = $('#github-repo').val().trim();
        const version = $('#github-branch').val().trim();
        const repoType = $('#repo-type-install').val() || 'github';

        // Validate input
        if (!repo) {
            $status.show().html('<div class="qu-error-state"><span class="dashicons dashicons-warning"></span> Please enter a repository path.</div>');
            $('#github-repo').focus();
            return;
        }

        // Add loading state
        $button.prop('disabled', true).addClass('qu-loading');
        $button.find('.dashicons').addClass('qu-spin');

        // Update loading message based on repository type
        let loadingMessage = 'Installing plugin...';
        if (repoType === 'github') {
            loadingMessage = 'Installing plugin from GitHub...';
        } else if (repoType === 'gitlab') {
            loadingMessage = 'Installing plugin from GitLab...';
        } else if (repoType === 'bitbucket') {
            loadingMessage = 'Installing plugin from Bitbucket...';
        }

        $status.show().html(`<div class="qu-loading-indicator"><span class="dashicons dashicons-update-alt qu-spin"></span> ${loadingMessage}</div>`);

        // Format the repo string with the repository type prefix if not GitHub
        const formattedRepo = repoType !== 'github' ? `${repoType}:${repo}` : repo;

        $.ajax(addCSRFToQUpdaterAjax({
            url: qUpdater.ajaxurl,
            type: 'POST',
            data: {
                action: 'install_github_plugin',
                nonce: qUpdater.nonce,
                repo: formattedRepo,
                version: version
            },
            success: function(response) {
                if (response.success) {
                    $status.html(`<div class="qu-success-state"><span class="dashicons dashicons-yes"></span> ${response.data}</div>`);

                    // Show countdown
                    let countdown = 3;
                    const countdownInterval = setInterval(function() {
                        countdown--;
                        if (countdown <= 0) {
                            clearInterval(countdownInterval);
                            location.reload();
                        } else {
                            $status.append(`<div class="qu-countdown">Reloading page in ${countdown}...</div>`);
                        }
                    }, 1000);
                } else {
                    $status.html(`<div class="qu-error-state"><span class="dashicons dashicons-warning"></span> Error: ${response.data}</div>`);
                    $button.prop('disabled', false).removeClass('qu-loading');
                    $button.find('.dashicons').removeClass('qu-spin');
                }
            },
            error: function(xhr) {
                $status.html(`<div class="qu-error-state"><span class="dashicons dashicons-warning"></span> Error: ${xhr.responseJSON?.data || 'Connection failed'}</div>`);
                $button.prop('disabled', false).removeClass('qu-loading');
                $button.find('.dashicons').removeClass('qu-spin');
            }
        }));
    });

    // GitHub Plugin Search
    $('#search-github-plugins').on('click', function(e) {
        e.preventDefault();
        searchGitHubPlugins();
    });

    // Also trigger search on Enter key in search field
    $('#github-search').on('keypress', function(e) {
        if (e.which === 13) {
            e.preventDefault();
            searchGitHubPlugins();
        }
    });

    function searchGitHubPlugins() {
        const $button = $('#search-github-plugins');
        const $status = $('#github-search-status');
        const $results = $('#github-search-results');
        const query = $('#github-search').val().trim();
        const sort = $('#github-sort').val();
        const order = $('#github-order').val();
        const repoType = $('#repo-type').val() || 'github';

        // Validate input
        if (!query) {
            $status.html('<div class="qu-error-state"><span class="dashicons dashicons-warning"></span> Please enter a search query.</div>');
            $('#github-search').focus();
            return;
        }

        // Add loading state
        $button.prop('disabled', true);
        $button.find('.dashicons').addClass('qu-spin');

        // Update loading message based on repository type
        let loadingMessage = 'Searching for plugins...';
        if (repoType === 'github') {
            loadingMessage = 'Searching GitHub for plugins...';
        } else if (repoType === 'gitlab') {
            loadingMessage = 'Searching GitLab for plugins...';
        } else if (repoType === 'bitbucket') {
            loadingMessage = 'Searching Bitbucket for plugins...';
        } else if (repoType === 'all') {
            loadingMessage = 'Searching all repositories for plugins...';
        }

        $results.html(`<div class="qu-loading-indicator"><span class="dashicons dashicons-update-alt qu-spin"></span> ${loadingMessage}</div>`);
        $status.html('');

        $.ajax(addCSRFToQUpdaterAjax({
            url: qUpdater.ajaxurl,
            type: 'POST',
            data: {
                action: 'search_github_plugins',
                nonce: qUpdater.bulk_update_nonce,
                query: query,
                sort: sort,
                order: order,
                repo_type: repoType
            },
            success: function(response) {
                $button.prop('disabled', false);
                $button.find('.dashicons').removeClass('qu-spin');

                if (response.success && response.data.length > 0) {
                    let html = '';

                    response.data.forEach(function(plugin) {
                        // Format date
                        const updatedDate = new Date(plugin.updated_at).toLocaleDateString();

                        // Get repository icon based on source
                        let repoIcon = 'dashicons-admin-site';
                        if (plugin.source === 'github') {
                            repoIcon = 'dashicons-github';
                        } else if (plugin.source === 'gitlab') {
                            repoIcon = 'dashicons-admin-network';
                        } else if (plugin.source === 'bitbucket') {
                            repoIcon = 'dashicons-database';
                        }

                        // Create plugin card
                        html += `
                        <div class="qu-plugin-card">
                            <div class="qu-plugin-header">
                                <div class="qu-plugin-source">
                                    <span class="dashicons ${repoIcon}" title="Source: ${plugin.source || 'GitHub'}"></span>
                                </div>
                                <h3 class="qu-plugin-title">
                                    <a href="${plugin.html_url}" target="_blank">${plugin.name}</a>
                                </h3>
                            </div>

                            <div class="qu-plugin-meta">
                                <div class="qu-plugin-meta-item">
                                    <span class="dashicons dashicons-star-filled"></span> ${plugin.stars}
                                </div>
                                <div class="qu-plugin-meta-item">
                                    <span class="dashicons dashicons-networking"></span> ${plugin.forks}
                                </div>
                                <div class="qu-plugin-meta-item">
                                    <span class="dashicons dashicons-calendar-alt"></span> Updated: ${updatedDate}
                                </div>
                            </div>

                            <p class="qu-plugin-description">${plugin.description || 'No description available'}</p>

                            <div class="qu-plugin-footer">
                                <div class="qu-plugin-author">
                                    <img src="${plugin.owner.avatar_url}" alt="${plugin.owner.login}" class="qu-author-avatar">
                                    <div class="qu-author-name">by <a href="${plugin.owner.html_url}" target="_blank">${plugin.owner.login}</a></div>
                                </div>

                                <div class="qu-plugin-actions">
                                    <button type="button" class="qu-button qu-button-primary qu-install-search-plugin"
                                        data-repo="${plugin.source ? plugin.source + ':' : ''}${plugin.full_name}">
                                        <span class="dashicons dashicons-download"></span> Install
                                    </button>
                                    <a href="${plugin.html_url}" target="_blank" class="qu-button qu-button-secondary">
                                        <span class="dashicons dashicons-external"></span> View Repository
                                    </a>
                                </div>
                            </div>
                        </div>`;
                    });

                    $results.html(html);
                } else if (response.success && response.data.length === 0) {
                    $results.html('<div class="qu-empty-state"><span class="dashicons dashicons-info"></span> No plugins found matching your search criteria.</div>');
                } else {
                    $results.html('<div class="qu-empty-state"><span class="dashicons dashicons-info"></span> No plugins found.</div>');
                    $status.html(`<div class="qu-error-state"><span class="dashicons dashicons-warning"></span> Error: ${response.data}</div>`);
                }
            },
            error: function(xhr) {
                $button.prop('disabled', false);
                $button.find('.dashicons').removeClass('qu-spin');
                $results.html('<div class="qu-empty-state"><span class="dashicons dashicons-info"></span> Search failed.</div>');
                $status.html(`<div class="qu-error-state"><span class="dashicons dashicons-warning"></span> Error: ${xhr.responseJSON?.data || 'Connection failed'}</div>`);
            }
        }));
    }

    // Handle installing plugins from search results
    $(document).on('click', '.qu-install-search-plugin', function(e) {
        e.preventDefault();

        const $button = $(this);
        const repo = $button.data('repo');

        if (!confirm(`Are you sure you want to install the plugin from ${repo}?`)) {
            return;
        }

        // Add loading state
        $button.prop('disabled', true);
        $button.html('<span class="dashicons dashicons-update qu-spin"></span> Installing...');

        $.ajax(addCSRFToQUpdaterAjax({
            url: qUpdater.ajaxurl,
            type: 'POST',
            data: {
                action: 'install_github_plugin',
                nonce: qUpdater.bulk_update_nonce,
                repo: repo,
                version: ''
            },
            success: function(response) {
                if (response.success) {
                    alert(response.data);
                    location.reload();
                } else {
                    alert('Error: ' + response.data);
                    $button.prop('disabled', false);
                    $button.html('<span class="dashicons dashicons-download"></span> Install');
                }
            },
            error: function(xhr) {
                alert('Installation failed: ' + (xhr.responseJSON?.data || 'Connection error'));
                $button.prop('disabled', false);
                $button.html('<span class="dashicons dashicons-download"></span> Install');
            }
        }));
    });



    // Performance monitoring for tab switching
    const TabPerformanceMonitor = {
        startTime: null,

        startTiming: function(tabId) {
            this.startTime = performance.now();
            console.log(`Starting tab switch to: ${tabId}`);
        },

        endTiming: function(tabId) {
            if (this.startTime) {
                const endTime = performance.now();
                const duration = endTime - this.startTime;
                console.log(`Tab switch to ${tabId} completed in ${duration.toFixed(2)}ms`);

                // Log slow tab switches (over 500ms)
                if (duration > 500) {
                    console.warn(`Slow tab switch detected: ${tabId} took ${duration.toFixed(2)}ms`);
                }

                this.startTime = null;
            }
        }
    };

    // Add performance monitoring to tab switches if in debug mode
    if (typeof qUpdater !== 'undefined' && qUpdater.debug) {
        $(document).on('click', '.qu-nav-tab-js', function() {
            const tabId = $(this).data('tab');
            if (tabId) {
                TabPerformanceMonitor.startTiming(tabId);
                setTimeout(() => TabPerformanceMonitor.endTiming(tabId), 100);
            }
        });
    }
});
