# Copyright (C) 2025 Shamielo
# This file is distributed under the GPL2.
msgid ""
msgstr ""
"Project-Id-Version: Q-Updater 1.4.0\n"
"Report-Msgid-Bugs-To: https://github.com/shamielo/q-updater/issues\n"
"POT-Creation-Date: 2024-12-19\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"

#: includes/class-notifications.php:68
msgid "Q-Updater: GitHub access token is not configured. Some features may be limited."
msgstr ""

#: includes/class-notifications.php:70
msgid "Configure now"
msgstr ""

#: includes/class-notifications.php:77
msgid "Q-Updater settings saved successfully."
msgstr ""

#: includes/class-settings.php:312
msgid "Active"
msgstr ""

#: includes/class-settings.php:312
msgid "Inactive"
msgstr ""

#: includes/class-settings.php:166
msgid "Settings saved successfully."
msgstr ""

#: includes/class-settings.php:294
msgid "Error checking updates"
msgstr ""

#: includes/class-settings.php:295
msgid "Update Available"
msgstr ""

#: includes/class-settings.php:295
msgid "Up to Date"
msgstr ""

#: includes/class-github-api.php:44
msgid "GitHub API Error: %s"
msgstr ""

#: includes/class-github-api.php:53
msgid "GitHub API Error: HTTP %s"
msgstr ""

#: includes/class-updates.php:264
msgid "Failed to check updates for %s: %s"
msgstr ""