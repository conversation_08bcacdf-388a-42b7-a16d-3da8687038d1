# Q-Updater

A WordPress plugin that automatically updates Q plugins from GitHub repositories.

## Features

- **Automatic Updates**: Keep your Q plugins up-to-date automatically
- **GitHub Integration**: Pull updates directly from GitHub repositories
- **Flexible Configuration**: Control update frequency and notification settings
- **Version Management**: Roll back to previous versions if needed
- **Email Notifications**: Get notified when updates are available or installed
- **Dashboard Notifications**: See update information directly in your WordPress dashboard
- **Plugin Analytics**: Track plugin usage and update statistics
- **Review System**: Submit and view plugin reviews
- **Security Features**: Secure token storage and API communication

## Project Status & Roadmap

### Completed Features (Q1-Q2 2023)

- ✅ Basic GitHub API integration
- ✅ Plugin update detection system
- ✅ Settings page with GitHub token configuration
- ✅ Integration with WordPress update system
- ✅ Manual update functionality

### Completed Features (Q3-Q4 2023)

- ✅ Automatic update scheduling (hourly, daily, weekly, monthly)
- ✅ Email notification system
- ✅ Admin dashboard notifications
- ✅ Basic plugin management interface
- ✅ Version history tracking (storing last 5 versions)

### Completed Features (Q1-Q2 2024)

- ✅ Plugin analytics system
- ✅ Review submission and management
- ✅ Enhanced security with token encryption
- ✅ Rollback functionality
- ✅ Comprehensive documentation

### Completed Features (Q3-Q4 2024)

- ✅ UI/UX improvements for all admin pages
- ✅ Enhanced analytics dashboard
- ✅ Batch update functionality
- ✅ Dependency resolution for plugin updates
- ✅ Health monitoring system

### In Progress (Q1-Q2 2025)

- 🔄 Custom branch support for updates
- 🔄 WP-CLI integration for headless management
- 🔄 Automated testing and continuous integration
- 🔄 Advanced performance optimizations

### Planned Features (Q3-Q4 2025)

- ⏳ Multi-site support enhancements
- ⏳ Plugin marketplace integration
- ⏳ Developer API for third-party extensions
- ⏳ Internationalization improvements
- ⏳ Advanced logging and debugging tools

## Production Release

Q-Updater v1.4.0 is now production-ready with comprehensive features including:

- **Complete Security Implementation**: Full input validation, CSRF protection, and secure token storage
- **Professional UI/UX**: Modern, responsive interface with accessibility compliance
- **Advanced Analytics**: Comprehensive tracking and reporting with Chart.js integration
- **Robust Error Handling**: Detailed error messages and recovery mechanisms
- **Performance Optimized**: Efficient database queries and optimized asset loading
- **Comprehensive Documentation**: Complete user and developer guides
- **Multi-Repository Support**: GitHub, GitLab, and Bitbucket integration
- **Enterprise Features**: Batch updates, rollback functionality, and health monitoring

## Documentation

Comprehensive documentation is available in the [docs](docs/index.md) directory:

- [User Guide](docs/user-guide.md)
- [Installation Guide](docs/installation.md)
- [Configuration Guide](docs/configuration.md)
- [Troubleshooting Guide](docs/troubleshooting.md)
- [FAQ](docs/faq.md)
- [Developer Documentation](docs/developer/index.md)

## Quick Installation

1. Download the plugin ZIP file
2. Go to WordPress Admin > Plugins > Add New
3. Click "Upload Plugin" and select the ZIP file
4. Activate the plugin

For detailed installation instructions, see the [Installation Guide](docs/installation.md).

## Quick Configuration

After installation, you should configure the following settings:

### GitHub API Token

For private repositories or to avoid GitHub API rate limits, configure a GitHub Personal Access Token:

1. Go to [GitHub Personal Access Tokens](https://github.com/settings/tokens/new)
2. Create a new token with the "repo" scope
3. Copy the token and paste it in the Q-Updater settings

### Update Frequency

Choose how often Q-Updater checks for plugin updates (hourly, daily, weekly, monthly, or manual only).

### Notification Settings

Configure email and dashboard notifications for update events.

For detailed configuration instructions, see the [Configuration Guide](docs/configuration.md).

## Key Features

### Plugin Management

- **Auto-Updates**: Enable automatic updates for specific plugins ✅
- **Manual Updates**: Update plugins individually or in bulk ✅
- **Plugin Installation**: Install Q plugins directly from GitHub ✅
- **Dependency Resolution**: Automatically handle plugin dependencies 🔄

### Version Control

- **Rollback**: Revert to previous versions if needed ✅
- **Version History**: Track all plugin version changes ✅
- **Backup**: Automatic backup before updates ✅

### Analytics and Reviews

- **Usage Tracking**: Monitor plugin installations and updates ✅
- **Performance Metrics**: Track update success rates and errors ✅
- **Review System**: Submit and view plugin reviews ✅

### Security

- **Token Encryption**: Secure storage of GitHub API tokens ✅
- **CSRF Protection**: Protection against cross-site request forgery ✅
- **Input Validation**: Thorough validation of all user inputs ✅
- **Permission Checks**: Strict permission checks for all actions ✅

### Developer Tools

- **Hooks & Filters**: Extensive API for extending functionality ✅
- **WP-CLI Support**: Command-line interface for headless management ⏳
- **Developer Documentation**: Comprehensive guides for extending the plugin ✅

For detailed usage instructions, see the [User Guide](docs/user-guide.md).

## Current Version

**Version 1.4.0** - Released December 2024

The current stable version includes all completed features listed in the roadmap through Q3-Q4 2024, representing a production-ready release with comprehensive functionality. See the [changelog](docs/changelog.md) for detailed release notes.

## System Requirements

- WordPress 5.6 or higher
- PHP 7.4 or higher
- MySQL 5.6 or higher
- PHP cURL extension enabled
- PHP OpenSSL extension required (for secure token storage)
- Modern web browser with JavaScript enabled

## Support and Contributing

### Getting Help

If you need help with Q-Updater:

1. Check the [Documentation](docs/index.md)
2. Review the [Troubleshooting Guide](docs/troubleshooting.md) and [FAQ](docs/faq.md)
3. Open an issue on the [GitHub repository](https://github.com/shamielo/q-updater)

### Contributing

Contributions to Q-Updater are welcome! Please see the [Developer Documentation](docs/developer/index.md) for guidelines.

## License

Q-Updater is licensed under the GPL2 license. See the LICENSE file for details.
