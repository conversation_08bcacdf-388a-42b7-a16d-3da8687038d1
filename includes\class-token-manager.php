<?php
/**
 * Token Manager Class
 *
 * Handles GitHub token management, renewal reminders, and token security
 *
 * @package Q-Updater
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class Q_Updater_Token_Manager
{
    private $parent;
    private $security;
    private $encryption;

    /**
     * Constructor
     *
     * @param Q_Updater $parent Parent class instance
     */
    public function __construct($parent)
    {
        $this->parent = $parent;
        $this->security = $parent->get_security();
        $this->encryption = $parent->get_encryption();

        // Register hooks
        add_action('admin_notices', [$this, 'display_token_notices']);
        add_action('q_updater_token_renewal_reminder', [$this, 'send_token_renewal_email']);
    }

    /**
     * Display token-related admin notices
     */
    public function display_token_notices()
    {
        // Only show notices to users who can manage options
        if (!current_user_can('manage_options')) {
            return;
        }

        // Get the encrypted GitHub token
        $encrypted_token = get_option($this->parent->get_option_name('github_token'), '');

        if (empty($encrypted_token)) {
            // Token is not set
            if (!get_user_meta(get_current_user_id(), 'q_updater_dismissed_token_notice', true)) {
                echo '<div class="notice notice-warning is-dismissible" data-notice="token_missing">
                    <p>' . __('Q-Updater: GitHub access token is not configured. Some features may be limited.', 'q-updater') .
                    ' <a href="' . admin_url('options-general.php?page=q-updater&tab=settings') . '">' .
                    __('Configure now', 'q-updater') . '</a></p></div>';
            }
            return;
        }

        // Check token expiration
        $token_expiration = $this->security->check_token_expiration($encrypted_token);

        if ($token_expiration) {
            if (isset($token_expiration['expired']) && $token_expiration['expired']) {
                // Token is expired
                echo '<div class="notice notice-error is-dismissible">
                    <p>' . sprintf(
                    __('Q-Updater: Your GitHub token has expired %d days ago. Please <a href="%s">generate a new token</a>.', 'q-updater'),
                    $token_expiration['days_ago'],
                    admin_url('options-general.php?page=q-updater&tab=settings')
                ) . '</p></div>';
            } else if (isset($token_expiration['expiring_soon']) && $token_expiration['expiring_soon']) {
                // Token is expiring soon
                echo '<div class="notice notice-warning is-dismissible">
                    <p>' . sprintf(
                    __('Q-Updater: Your GitHub token will expire in %d days. Please <a href="%s">generate a new token</a> before it expires.', 'q-updater'),
                    $token_expiration['days_remaining'],
                    admin_url('options-general.php?page=q-updater&tab=settings')
                ) . '</p></div>';
            } else if (isset($token_expiration['aging']) && $token_expiration['aging']) {
                // Token is old
                echo '<div class="notice notice-info is-dismissible">
                    <p>' . sprintf(
                    __('Q-Updater: Your GitHub token is %d days old. Consider <a href="%s">generating a new token</a> for security.', 'q-updater'),
                    $token_expiration['days_old'],
                    admin_url('options-general.php?page=q-updater&tab=settings')
                ) . '</p></div>';
            }
        }
    }

    /**
     * Send token renewal email reminder
     */
    public function send_token_renewal_email()
    {
        // Get admin email
        $admin_email = get_option('admin_email');

        // Get developer email if set
        $developer_email = get_option($this->parent->get_option_name('developer_email'), '');

        // Use developer email if available, otherwise use admin email
        $to = !empty($developer_email) ? $developer_email : $admin_email;

        // Get token expiration info
        $encrypted_token = get_option($this->parent->get_option_name('github_token'), '');
        $token_expiration = $this->security->check_token_expiration($encrypted_token);

        if (!$token_expiration || !isset($token_expiration['days_remaining'])) {
            return;
        }

        $days_remaining = $token_expiration['days_remaining'];

        $subject = sprintf(
            __('[%s] GitHub Token Expiration Reminder', 'q-updater'),
            get_bloginfo('name')
        );

        $message = sprintf(
            __('Hello,

This is an automated reminder that the GitHub token used by Q-Updater on your WordPress site (%s) will expire in %d days.

Please generate a new GitHub token and update it in the Q-Updater settings to ensure continuous functionality.

To update your token:
1. Go to GitHub and generate a new token: https://github.com/settings/tokens/new
2. Log in to your WordPress admin
3. Navigate to Settings > Q-Updater
4. Enter your new token in the GitHub Access Token field

If you have any questions, please contact your site administrator.

Thank you,
Q-Updater Plugin', 'q-updater'),
            get_bloginfo('name'),
            $days_remaining
        );

        $headers = [
            'Content-Type: text/plain; charset=UTF-8',
            'From: ' . get_bloginfo('name') . ' <' . $admin_email . '>'
        ];

        wp_mail($to, $subject, $message, $headers);

        // Log the reminder
        error_log(sprintf(
            'Q-Updater: Token renewal reminder sent to %s. Token expires in %d days.',
            $to,
            $days_remaining
        ));
    }

    /**
     * Validate and save GitHub token
     *
     * @param string $token GitHub token to validate and save
     * @return array Result with status and message
     */
    public function validate_and_save_token($token)
    {
        // Sanitize token
        $token = sanitize_text_field($token);

        // Check if token is empty
        if (empty($token)) {
            // Clear the token
            delete_option($this->parent->get_option_name('github_token'));

            return [
                'status' => 'warning',
                'message' => __('GitHub token has been cleared. Some features may be limited.', 'q-updater')
            ];
        }

        // Validate token format
        if (!$this->security->validate_github_token($token)) {
            return [
                'status' => 'error',
                'message' => __('Invalid GitHub token format. Please enter a valid GitHub Personal Access Token.', 'q-updater')
            ];
        }

        // Encrypt the token using enhanced encryption
        $encrypted_token = $this->encryption->encrypt_github_token($token);

        // Save the token
        update_option($this->parent->get_option_name('github_token'), $encrypted_token);

        // Set token expiration reminder for 60 days from now
        $this->security->set_token_expiration(time() + (60 * DAY_IN_SECONDS));

        // Set token creation date
        $this->security->set_token_creation(time());

        // Schedule token renewal reminder
        $this->security->schedule_token_renewal_reminder(15); // Remind 15 days before expiration

        // Fire action for token sync
        do_action('q_updater_token_updated');

        return [
            'status' => 'success',
            'message' => __('GitHub token saved successfully. Token will expire in 60 days.', 'q-updater')
        ];
    }

    /**
     * Check if a string looks like a valid GitHub token format
     *
     * @param string $token Token to validate
     * @return bool True if the token appears to be valid
     */
    private function is_valid_token_format($token)
    {
        if (empty($token)) {
            return false;
        }

        // GitHub tokens are typically 40+ characters of alphanumeric characters
        // This is a simple check to avoid trying to use obviously invalid tokens
        return (bool) preg_match('/^[a-zA-Z0-9_]{30,}$/', $token);
    }

    /**
     * Test GitHub token validity by making an API request
     *
     * @return array Result with status and message
     */
    public function test_token()
    {
        // Get the encrypted GitHub token
        $encrypted_token = get_option($this->parent->get_option_name('github_token'), '');

        if (empty($encrypted_token)) {
            error_log('Q-Updater: No GitHub token is configured');
            return [
                'status' => 'error',
                'message' => __('No GitHub token is configured.', 'q-updater')
            ];
        }

        // Try to decrypt the token using both security and encryption classes
        $token = '';

        // First try using the encryption class if it exists
        if ($this->encryption !== null && method_exists($this->encryption, 'decrypt_github_token')) {
            $token = $this->encryption->decrypt_github_token($encrypted_token);
            if (!empty($token)) {
                error_log('Q-Updater: Successfully decrypted GitHub token using encryption class');
            }
        }

        // If that fails, try the security class if it exists
        if (empty($token) && $this->security !== null && method_exists($this->security, 'decrypt')) {
            $token = $this->security->decrypt($encrypted_token);
            if (!empty($token)) {
                error_log('Q-Updater: Successfully decrypted GitHub token using security class');
            }
        }

        // If both methods fail, try a simple base64 decode as a last resort
        if (empty($token) && !empty($encrypted_token)) {
            error_log('Q-Updater: Attempting simple base64 decode as fallback');
            $decoded = base64_decode($encrypted_token, true);
            if ($decoded !== false && $this->is_valid_token_format($decoded)) {
                $token = $decoded;
                error_log('Q-Updater: Successfully decoded token using base64 fallback');
            }
        }

        if (empty($token)) {
            error_log('Q-Updater: Failed to decrypt GitHub token');
            return [
                'status' => 'error',
                'message' => __('Failed to decrypt GitHub token.', 'q-updater')
            ];
        }

        // Validate token format
        if (!$this->security->validate_github_token($token)) {
            error_log('Q-Updater: Invalid GitHub token format');
            return [
                'status' => 'error',
                'message' => __('Invalid GitHub token format.', 'q-updater')
            ];
        }

        // Make a test API request
        $url = 'https://api.github.com/user';
        $args = [
            'headers' => [
                'Authorization' => 'Bearer ' . $token, // GitHub now requires 'Bearer' instead of 'token'
                'User-Agent' => 'WordPress/Q-Updater'
            ],
            'timeout' => 15,
            'sslverify' => true
        ];

        error_log('Q-Updater: Testing GitHub token with API request to ' . $url);
        $response = wp_remote_get($url, $args);

        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            error_log('Q-Updater: GitHub API Error: ' . $error_message);
            return [
                'status' => 'error',
                'message' => sprintf(__('API Error: %s', 'q-updater'), $error_message)
            ];
        }

        $response_code = wp_remote_retrieve_response_code($response);

        // Log rate limit information
        $rate_limit = wp_remote_retrieve_header($response, 'x-ratelimit-limit');
        $rate_remaining = wp_remote_retrieve_header($response, 'x-ratelimit-remaining');
        $rate_reset = wp_remote_retrieve_header($response, 'x-ratelimit-reset');

        if ($rate_limit && $rate_remaining) {
            error_log("Q-Updater: GitHub API Rate Limit: {$rate_remaining}/{$rate_limit} remaining");
        }

        if ($response_code !== 200) {
            $body = wp_remote_retrieve_body($response);
            $error_data = json_decode($body, true);
            $error_message = isset($error_data['message']) ? $error_data['message'] : 'Unknown error';

            error_log("Q-Updater: GitHub API Error HTTP {$response_code}: {$error_message}");

            // Check for specific error types
            if ($response_code === 401) {
                return [
                    'status' => 'error',
                    'message' => __('Authentication failed. The token may be invalid or expired.', 'q-updater')
                ];
            } else if ($response_code === 403 && strpos($error_message, 'rate limit') !== false) {
                $reset_time = $rate_reset ? date('H:i:s', $rate_reset) : 'unknown time';
                return [
                    'status' => 'error',
                    'message' => sprintf(__('Rate limit exceeded. Limit will reset at %s.', 'q-updater'), $reset_time)
                ];
            } else {
                return [
                    'status' => 'error',
                    'message' => sprintf(__('API Error: HTTP %s - %s', 'q-updater'), $response_code, $error_message)
                ];
            }
        }

        // Get user data
        $user_data = json_decode(wp_remote_retrieve_body($response), true);

        if (!isset($user_data['login'])) {
            error_log('Q-Updater: Invalid response from GitHub API - missing login information');
            return [
                'status' => 'error',
                'message' => __('Invalid response from GitHub API.', 'q-updater')
            ];
        }

        // Update last successful API call timestamp
        $this->security->update_last_successful_api_call();

        // Check token scopes
        $scopes = wp_remote_retrieve_header($response, 'x-oauth-scopes');
        $has_repo_scope = $scopes && strpos($scopes, 'repo') !== false;

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("Q-Updater: GitHub token scopes: {$scopes}");
        }

        if (!$has_repo_scope) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Q-Updater: GitHub token is missing repo scope');
            }
            return [
                'status' => 'warning',
                'message' => sprintf(
                    __('Token is valid (authenticated as %s), but missing "repo" scope. Some features may be limited.', 'q-updater'),
                    $user_data['login']
                ),
                'user' => $user_data,
                'scopes' => $scopes
            ];
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("Q-Updater: GitHub token is valid - authenticated as {$user_data['login']}");
        }
        return [
            'status' => 'success',
            'message' => sprintf(__('Token is valid. Authenticated as GitHub user: %s', 'q-updater'), $user_data['login']),
            'user' => $user_data,
            'scopes' => $scopes,
            'rate_limit' => [
                'limit' => $rate_limit,
                'remaining' => $rate_remaining,
                'reset' => $rate_reset ? date('H:i:s', $rate_reset) : null
            ]
        ];
    }
}
