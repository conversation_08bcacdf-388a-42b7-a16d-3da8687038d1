<?php
/**
 * Cron Manager Class
 *
 * Handles WordPress cron operations with improved error handling and reliability.
 *
 * @package Q-Updater
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class Q_Updater_Cron_Manager
{
    private $parent;
    private $debug_mode = false;

    /**
     * Constructor
     *
     * @param Q_Updater $parent Parent class instance
     */
    public function __construct($parent)
    {
        $this->parent = $parent;
        $this->debug_mode = defined('WP_DEBUG') && WP_DEBUG;

        // Add hooks to handle cron errors
        add_action('cron_reschedule_event_error', [$this, 'log_cron_error'], 10, 3);
        add_action('cron_unschedule_event_error', [$this, 'log_cron_error'], 10, 3);
    }

    /**
     * Schedule a cron event with improved error handling
     *
     * @param string $hook Action hook to execute when the event is run
     * @param int $timestamp Unix timestamp (UTC) for when to next run the event
     * @param string $recurrence How often the event should recur (hourly, daily, etc.)
     * @param array $args Optional arguments to pass to the hook's callback function
     * @return bool|WP_Error True if event successfully scheduled, WP_Error on failure
     */
    public function schedule_event($hook, $timestamp, $recurrence, $args = [])
    {
        // Validate input parameters
        if (empty($hook) || !is_string($hook)) {
            $this->log_error('Invalid hook parameter provided to schedule_event');
            return new WP_Error('invalid_parameter', 'Invalid hook parameter');
        }

        if (!is_numeric($timestamp)) {
            $this->log_error('Invalid timestamp parameter provided to schedule_event');
            return new WP_Error('invalid_parameter', 'Invalid timestamp parameter');
        }

        if (empty($recurrence) || !is_string($recurrence)) {
            $this->log_error('Invalid recurrence parameter provided to schedule_event');
            return new WP_Error('invalid_parameter', 'Invalid recurrence parameter');
        }

        if (!is_array($args)) {
            $args = [];
        }

        // Clear any existing scheduled events for this hook and args
        $this->clear_scheduled_event($hook, $args);

        // Add a small random delay to prevent race conditions (1-5 seconds)
        $timestamp += mt_rand(1, 5);

        // Try to schedule the event
        $result = wp_schedule_event($timestamp, $recurrence, $hook, $args);

        if (is_wp_error($result)) {
            $this->log_error(sprintf(
                'Failed to schedule cron event for hook: %s, Error: %s',
                $hook,
                $result->get_error_message()
            ));
            return $result;
        }

        if ($this->debug_mode) {
            $this->log_debug(sprintf(
                'Successfully scheduled cron event for hook: %s, Next run: %s',
                $hook,
                date('Y-m-d H:i:s', $timestamp)
            ));
        }

        return true;
    }

    /**
     * Clear a scheduled cron event with improved error handling
     *
     * @param string $hook Action hook of the event to unschedule
     * @param array $args Optional arguments passed to the hook's callback function
     * @return bool|WP_Error True if event successfully unscheduled, WP_Error on failure
     */
    public function clear_scheduled_event($hook, $args = [])
    {
        // Validate input parameters
        if (empty($hook) || !is_string($hook)) {
            $this->log_error('Invalid hook parameter provided to clear_scheduled_event');
            return new WP_Error('invalid_parameter', 'Invalid hook parameter');
        }

        if (!is_array($args)) {
            $args = [];
        }

        $timestamp = wp_next_scheduled($hook, $args);

        if (!$timestamp) {
            // No event scheduled, so nothing to clear
            return true;
        }

        // Try to unschedule the event
        $result = wp_unschedule_event($timestamp, $hook, $args);

        if (is_wp_error($result)) {
            $this->log_error(sprintf(
                'Failed to unschedule cron event for hook: %s, Error: %s',
                $hook,
                $result->get_error_message()
            ));
            return $result;
        }

        if ($this->debug_mode) {
            $this->log_debug(sprintf(
                'Successfully unscheduled cron event for hook: %s',
                $hook
            ));
        }

        return true;
    }

    /**
     * Reschedule a cron event with improved error handling
     *
     * @param string $hook Action hook of the event to reschedule
     * @param string $recurrence How often the event should recur
     * @param array $args Optional arguments passed to the hook's callback function
     * @return bool|WP_Error True if event successfully rescheduled, WP_Error on failure
     */
    public function reschedule_event($hook, $recurrence, $args = [])
    {
        // Validate input parameters
        if (empty($hook) || !is_string($hook)) {
            $this->log_error('Invalid hook parameter provided to reschedule_event');
            return new WP_Error('invalid_parameter', 'Invalid hook parameter');
        }

        if (empty($recurrence) || !is_string($recurrence)) {
            $this->log_error('Invalid recurrence parameter provided to reschedule_event');
            return new WP_Error('invalid_parameter', 'Invalid recurrence parameter');
        }

        if (!is_array($args)) {
            $args = [];
        }

        // Clear any existing scheduled events for this hook and args
        $this->clear_scheduled_event($hook, $args);

        // Schedule a new event
        return $this->schedule_event($hook, time(), $recurrence, $args);
    }

    /**
     * Clean up orphaned cron events
     *
     * This method attempts to fix the cron option by removing any invalid entries
     *
     * @return bool True if cleanup was successful
     */
    public function cleanup_cron_option()
    {
        $cron = _get_cron_array();

        if (!is_array($cron)) {
            $this->log_error('Failed to get cron array, returned value is not an array');
            return false;
        }

        $modified = false;
        $plugin_hooks = [
            'q_updater_background_update',
            'q_updater_auto_update',
            'q_updater_check_updates',
            'qu_weekly_analytics_report'
        ];

        // Loop through each timestamp
        foreach ($cron as $timestamp => $hooks) {
            // Check if timestamp is valid
            if (!is_numeric($timestamp) || $timestamp <= 0) {
                unset($cron[$timestamp]);
                $modified = true;
                continue;
            }

            // Validate hooks is an array
            if (!is_array($hooks)) {
                unset($cron[$timestamp]);
                $modified = true;
                continue;
            }

            // Loop through each hook
            foreach ($hooks as $hook => $events) {
                // Validate hook is a string
                if (!is_string($hook) || empty($hook)) {
                    unset($cron[$timestamp][$hook]);
                    $modified = true;
                    continue;
                }

                // Validate events is an array
                if (!is_array($events)) {
                    unset($cron[$timestamp][$hook]);
                    $modified = true;
                    continue;
                }

                // Check if this is one of our plugin hooks
                if (in_array($hook, $plugin_hooks)) {
                    // Loop through each event for this hook
                    foreach ($events as $key => $event) {
                        // Validate event is an array
                        if (!is_array($event)) {
                            unset($cron[$timestamp][$hook][$key]);
                            $modified = true;
                            continue;
                        }

                        // Check if the event has a valid schedule
                        if (isset($event['schedule'])) {
                            // If schedule is false, it's a one-time event, which is valid
                            if ($event['schedule'] === false) {
                                continue;
                            }

                            // If schedule is not a string, it's invalid
                            if (!is_string($event['schedule'])) {
                                unset($cron[$timestamp][$hook][$key]);
                                $modified = true;
                                continue;
                            }

                            $schedule = $event['schedule'];
                            $schedules = wp_get_schedules();

                            // Check if the schedule exists
                            if (!isset($schedules[$schedule])) {
                                // Invalid schedule, remove this event
                                unset($cron[$timestamp][$hook][$key]);
                                $modified = true;

                                $this->log_debug(sprintf(
                                    'Removed invalid cron event for hook: %s with invalid schedule: %s',
                                    $hook,
                                    $schedule
                                ));
                            }
                        }
                    }

                    // If no events left for this hook, remove the hook
                    if (empty($cron[$timestamp][$hook])) {
                        unset($cron[$timestamp][$hook]);
                        $modified = true;
                    }
                }
            }

            // If no hooks left for this timestamp, remove the timestamp
            if (empty($cron[$timestamp])) {
                unset($cron[$timestamp]);
                $modified = true;
            }
        }

        // If we made changes, update the cron option
        if ($modified) {
            $result = _set_cron_array($cron);
            if ($result) {
                $this->log_debug('Cleaned up cron option');
            } else {
                $this->log_error('Failed to update cron option after cleanup');
                return false;
            }
        }

        return true;
    }

    /**
     * Log cron errors from WordPress core
     *
     * @param WP_Error $error The error object
     * @param string $hook The hook that was being processed
     * @param array $event The event data
     */
    public function log_cron_error($error, $hook, $event)
    {
        if (!is_wp_error($error)) {
            return;
        }

        // Validate hook parameter
        $hook = (is_string($hook) && !empty($hook)) ? $hook : 'unknown_hook';

        // Get error message and code safely
        $error_message = $error->get_error_message();
        $error_code = $error->get_error_code();

        $this->log_error(sprintf(
            'Cron error for hook: %s, Error: %s, Code: %s',
            $hook,
            $error_message,
            $error_code
        ));

        // If this is a "could_not_set" error, try to clean up the cron option
        if ($error_code === 'could_not_set') {
            $this->cleanup_cron_option();

            // Validate event data
            if (!is_array($event)) {
                $this->log_error('Invalid event data provided to log_cron_error');
                return;
            }

            // Try to reschedule the event
            if (isset($event['schedule']) && $event['schedule'] !== false && is_string($event['schedule'])) {
                $args = isset($event['args']) && is_array($event['args']) ? $event['args'] : [];
                $this->reschedule_event($hook, $event['schedule'], $args);
            }
        }
    }

    /**
     * Log an error message
     *
     * @param string $message The error message
     */
    private function log_error($message)
    {
        // Only log errors if WP_DEBUG is enabled
        if (defined('WP_DEBUG') && WP_DEBUG) {
            // Ensure message is a string
            $message = is_string($message) ? $message : (is_object($message) ? get_class($message) : (is_array($message) ? 'Array' : (string) $message));
            error_log('Q-Updater Cron Error: ' . $message);
        }
    }

    /**
     * Log a debug message
     *
     * @param string $message The debug message
     */
    private function log_debug($message)
    {
        if ($this->debug_mode && defined('WP_DEBUG') && WP_DEBUG) {
            // Ensure message is a string
            $message = is_string($message) ? $message : (is_object($message) ? get_class($message) : (is_array($message) ? 'Array' : (string) $message));
            error_log('Q-Updater Cron Debug: ' . $message);
        }
    }
}
