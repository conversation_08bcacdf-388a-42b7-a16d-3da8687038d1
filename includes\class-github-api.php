<?php
/**
 * GitHub API Integration
 *
 * This class handles all communication with the GitHub API, including fetching
 * repository information, releases, and downloading plugin packages. It implements
 * secure API communication, token handling, and response validation.
 *
 * @package Q-Updater
 * @since 1.0.0
 * <AUTHOR>
 * @link https://github.com/shamielo/q-updater
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * GitHub API Integration class.
 *
 * Provides methods for interacting with the GitHub API to fetch repository
 * information, releases, and plugin packages. Handles authentication,
 * rate limiting, and response validation.
 *
 * @since 1.0.0
 */
class Q_Updater_GitHub_API
{
    /**
     * Parent plugin instance
     *
     * @since 1.0.0
     * @var Q_Updater
     */
    private $parent;

    /**
     * Security component instance
     *
     * @since 1.0.0
     * @var Q_Updater_Security
     */
    private $security;

    /**
     * Encryption component instance
     *
     * @since 1.0.0
     * @var Q_Updater_Encryption
     */
    private $encryption;

    /**
     * Constructor
     *
     * Initializes the GitHub API class with references to the parent plugin
     * and required component instances for security and encryption.
     *
     * @since 1.0.0
     * @param Q_Updater $parent Parent class instance
     */
    public function __construct($parent)
    {
        $this->parent = $parent;
        $this->security = $parent->get_security();
        $this->encryption = $parent->get_encryption();
    }

    /**
     * Get repository type and API base URL
     *
     * Determines the repository type (GitHub, GitLab, Bitbucket) based on the repository format
     * and returns the appropriate API base URL.
     *
     * @since 1.1.0
     * @param string $repo Repository in format "username/repo" or with prefix (gitlab:username/repo)
     * @return array Array with 'type' and 'api_base_url' keys
     */
    public function get_repository_api_info($repo)
    {
        // Default to GitHub
        $result = [
            'type' => 'github',
            'api_base_url' => 'https://api.github.com',
            'repo_path' => $repo
        ];

        // Check for GitLab prefix
        if (strpos($repo, 'gitlab:') === 0) {
            $result['type'] = 'gitlab';
            $result['api_base_url'] = 'https://gitlab.com/api/v4';
            $result['repo_path'] = substr($repo, 7); // Remove 'gitlab:' prefix
        }

        // Check for Bitbucket prefix
        else if (strpos($repo, 'bitbucket:') === 0) {
            $result['type'] = 'bitbucket';
            $result['api_base_url'] = 'https://api.bitbucket.org/2.0';
            $result['repo_path'] = substr($repo, 10); // Remove 'bitbucket:' prefix
        }

        return $result;
    }

    /**
     * Get API headers for repository
     *
     * Returns the appropriate headers for the repository type.
     *
     * @since 1.1.0
     * @param string $repo_type Repository type (github, gitlab, bitbucket)
     * @return array Headers for API requests
     */
    public function get_api_headers($repo_type = 'github')
    {
        $headers = ['User-Agent' => 'WordPress/Q-Updater'];

        switch ($repo_type) {
            case 'github':
                $headers = $this->get_github_headers();
                break;
            case 'gitlab':
                $headers = $this->get_gitlab_headers();
                break;
            case 'bitbucket':
                $headers = $this->get_bitbucket_headers();
                break;
        }

        return $headers;
    }

    /**
     * Get GitLab API headers
     *
     * Prepares the headers for GitLab API requests, including authentication
     * if a GitLab token is available. The token is retrieved from the database,
     * decrypted, and validated before being used in the PRIVATE-TOKEN header.
     *
     * @since 1.1.0
     * @return array Headers for GitLab API requests
     */
    public function get_gitlab_headers()
    {
        $headers = ['User-Agent' => 'WordPress/Q-Updater'];
        $encrypted_token = get_option($this->parent->get_option_name('gitlab_token'));

        error_log('Q-Updater: Preparing GitLab API headers');

        if (empty($encrypted_token)) {
            error_log('Q-Updater: No GitLab token found in options');
            return $headers;
        }

        // Try to decrypt the token
        $token = '';

        // First try using the security class
        if (method_exists($this->security, 'decrypt')) {
            $token = $this->security->decrypt($encrypted_token);
            if (!empty($token)) {
                error_log('Q-Updater: Successfully decrypted GitLab token using security class');
            }
        }

        // If that fails, try the encryption class
        if (empty($token) && method_exists($this->encryption, 'decrypt_token')) {
            $token = $this->encryption->decrypt_token($encrypted_token);
            if (!empty($token)) {
                error_log('Q-Updater: Successfully decrypted GitLab token using encryption class');
            }
        }

        if (empty($token)) {
            error_log('Q-Updater: Failed to decrypt GitLab token');
            return $headers;
        }

        // Validate token format before using
        if ($this->security->validate_gitlab_token($token)) {
            // GitLab uses PRIVATE-TOKEN header for authentication
            $headers['PRIVATE-TOKEN'] = $token;

            // Log success (without revealing the token)
            $token_prefix = substr($token, 0, 5) . '...';
            error_log("Q-Updater: Successfully added PRIVATE-TOKEN header with token prefix: {$token_prefix}");
        } else {
            // Log invalid token format
            error_log('Q-Updater: Invalid GitLab token format detected - PRIVATE-TOKEN header not added');
        }

        return $headers;
    }

    /**
     * Get Bitbucket API headers
     *
     * Prepares the headers for Bitbucket API requests, including authentication
     * if Bitbucket credentials are available. The credentials are retrieved from the database,
     * decrypted, and validated before being used in the Authorization header.
     *
     * @since 1.1.0
     * @return array Headers for Bitbucket API requests
     */
    public function get_bitbucket_headers()
    {
        $headers = ['User-Agent' => 'WordPress/Q-Updater'];
        $encrypted_username = get_option($this->parent->get_option_name('bitbucket_username'));
        $encrypted_app_password = get_option($this->parent->get_option_name('bitbucket_app_password'));

        error_log('Q-Updater: Preparing Bitbucket API headers');

        if (empty($encrypted_username) || empty($encrypted_app_password)) {
            error_log('Q-Updater: No Bitbucket credentials found in options');
            return $headers;
        }

        // Try to decrypt the credentials
        $username = '';
        $app_password = '';

        // First try using the security class
        if (method_exists($this->security, 'decrypt')) {
            $username = $this->security->decrypt($encrypted_username);
            $app_password = $this->security->decrypt($encrypted_app_password);
            if (!empty($username) && !empty($app_password)) {
                error_log('Q-Updater: Successfully decrypted Bitbucket credentials using security class');
            }
        }

        // If that fails, try the encryption class
        if ((empty($username) || empty($app_password)) && method_exists($this->encryption, 'decrypt_token')) {
            $username = $this->encryption->decrypt_token($encrypted_username);
            $app_password = $this->encryption->decrypt_token($encrypted_app_password);
            if (!empty($username) && !empty($app_password)) {
                error_log('Q-Updater: Successfully decrypted Bitbucket credentials using encryption class');
            }
        }

        if (empty($username) || empty($app_password)) {
            error_log('Q-Updater: Failed to decrypt Bitbucket credentials');
            return $headers;
        }

        // Create Basic Auth header
        $auth = base64_encode("$username:$app_password");
        $headers['Authorization'] = "Basic $auth";

        // Log success (without revealing the credentials)
        $username_prefix = substr($username, 0, 3) . '...';
        error_log("Q-Updater: Successfully added Authorization header for Bitbucket user: {$username_prefix}");

        return $headers;
    }

    /**
     * Get GitHub API headers
     *
     * Prepares the headers for GitHub API requests, including authentication
     * if a GitHub token is available. The token is retrieved from the database,
     * decrypted, and validated before being used in the Authorization header.
     *
     * The method attempts to decrypt the token using both the security and
     * encryption classes for backward compatibility.
     *
     * @since 1.0.0
     * @return array Headers for GitHub API requests
     */
    public function get_github_headers()
    {
        $headers = ['User-Agent' => 'WordPress/Q-Updater'];
        $encrypted_token = get_option($this->parent->get_option_name('github_token'));

        error_log('Q-Updater: Preparing GitHub API headers');

        if (empty($encrypted_token)) {
            error_log('Q-Updater: No GitHub token found in options');
            return $headers;
        }

        // Try to decrypt the token using both security and encryption classes
        $token = '';

        // First try using the security class
        if (method_exists($this->security, 'decrypt')) {
            $token = $this->security->decrypt($encrypted_token);
            if (!empty($token)) {
                error_log('Q-Updater: Successfully decrypted GitHub token using security class');
            }
        }

        // If that fails, try the encryption class
        if (empty($token) && method_exists($this->encryption, 'decrypt_github_token')) {
            $token = $this->encryption->decrypt_github_token($encrypted_token);
            if (!empty($token)) {
                error_log('Q-Updater: Successfully decrypted GitHub token using encryption class');
            }
        }

        if (empty($token)) {
            error_log('Q-Updater: Failed to decrypt GitHub token');
            return $headers;
        }

        // Validate token format before using
        if ($this->security->validate_github_token($token)) {
            // GitHub now requires 'Bearer' instead of 'token' for authorization
            $headers['Authorization'] = 'Bearer ' . $token;

            // Log success (without revealing the token)
            $token_prefix = substr($token, 0, 5) . '...';
            error_log("Q-Updater: Successfully added authorization header with token prefix: {$token_prefix}");

            // Update last successful API call timestamp if token is used
            $this->security->update_last_successful_api_call();
        } else {
            // Log invalid token format
            error_log('Q-Updater: Invalid GitHub token format detected - Authorization header not added');
        }

        return $headers;
    }

    /**
     * Search for plugins on GitHub
     *
     * Searches GitHub repositories for WordPress plugins matching the given query.
     * Results are sorted according to the specified criteria and include repository
     * information, README content, and owner details.
     *
     * The method implements input validation, secure API communication, rate limit
     * tracking, and response validation to ensure secure and reliable operation.
     *
     * @since 1.0.0
     * @param string $query Search query
     * @param string $sort Sort field (stars, forks, updated, help-wanted-issues)
     * @param string $order Sort order (desc, asc)
     * @return array|WP_Error Search results or error object on failure
     */
    public function search_plugins($query, $sort = 'stars', $order = 'desc')
    {
        // Validate and sanitize input parameters
        $query = sanitize_text_field($query);
        $sort = in_array($sort, ['stars', 'forks', 'updated', 'help-wanted-issues']) ? $sort : 'stars';
        $order = in_array($order, ['desc', 'asc']) ? $order : 'desc';

        // Add 'wordpress plugin' to the query to improve results
        $search_query = urlencode($query . ' wordpress plugin');
        $url = "https://api.github.com/search/repositories?q={$search_query}&sort={$sort}&order={$order}&per_page=10";

        // Validate and secure the API request
        $validated = $this->security->validate_github_api_request(
            $url,
            $this->get_github_headers()
        );

        $response = wp_remote_get(
            $validated['endpoint'],
            $validated['args']
        );

        // Get rate limit information from headers
        $rate_limit = wp_remote_retrieve_header($response, 'x-ratelimit-limit');
        $rate_limit_remaining = wp_remote_retrieve_header($response, 'x-ratelimit-remaining');

        if (is_wp_error($response)) {
            // Track API error in analytics
            $analytics = $this->parent->get_analytics();
            $analytics->track_github_api(
                'search_repositories',
                $rate_limit ? (int) $rate_limit : 0,
                $rate_limit_remaining ? (int) $rate_limit_remaining : 0,
                'error'
            );

            return new WP_Error(
                'github_api_error',
                sprintf(__('GitHub API Error: %s', 'q-updater'), $response->get_error_message())
            );
        }

        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            // Track API error in analytics
            $analytics = $this->parent->get_analytics();
            $analytics->track_github_api(
                'search_repositories',
                $rate_limit ? (int) $rate_limit : 0,
                $rate_limit_remaining ? (int) $rate_limit_remaining : 0,
                'error_' . $response_code
            );

            return new WP_Error(
                'github_api_error',
                sprintf(__('GitHub API Error: HTTP %s', 'q-updater'), $response_code)
            );
        }

        // Track successful API call in analytics
        $analytics = $this->parent->get_analytics();
        $analytics->track_github_api(
            'search_repositories',
            $rate_limit ? (int) $rate_limit : 0,
            $rate_limit_remaining ? (int) $rate_limit_remaining : 0,
            'success'
        );

        $data = json_decode(wp_remote_retrieve_body($response), true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return new WP_Error(
                'github_api_error',
                __('Invalid JSON response from GitHub API', 'q-updater')
            );
        }

        if (!isset($data['items']) || !is_array($data['items'])) {
            return [];
        }

        $results = [];
        foreach ($data['items'] as $repo) {
            // Get readme content to extract more plugin information
            $readme_content = $this->get_repo_readme($repo['full_name']);

            $results[] = [
                'name' => $repo['name'],
                'full_name' => $repo['full_name'],
                'description' => $repo['description'],
                'stars' => $repo['stargazers_count'],
                'forks' => $repo['forks_count'],
                'updated_at' => $repo['updated_at'],
                'html_url' => $repo['html_url'],
                'readme' => $readme_content,
                'owner' => [
                    'login' => $repo['owner']['login'],
                    'avatar_url' => $repo['owner']['avatar_url'],
                    'html_url' => $repo['owner']['html_url']
                ]
            ];
        }

        return $results;
    }

    /**
     * Get repository README content
     *
     * Fetches the README file content from a GitHub repository. The content
     * is returned as plain text after being decoded from base64 encoding.
     *
     * The method validates the repository name, secures the API request,
     * and validates the response to ensure security and reliability.
     *
     * @since 1.0.0
     * @param string $repo Repository name in the format "username/repo"
     * @return string README content as plain text or empty string on failure
     */
    public function get_repo_readme($repo)
    {
        // Validate repository name
        if (!$this->security->validate_repository_name($repo)) {
            return '';
        }

        $url = "https://api.github.com/repos/{$repo}/readme";

        // Validate and secure the API request
        $validated = $this->security->validate_github_api_request(
            $url,
            $this->get_github_headers()
        );

        $response = wp_remote_get(
            $validated['endpoint'],
            $validated['args']
        );

        if (is_wp_error($response) || wp_remote_retrieve_response_code($response) !== 200) {
            return '';
        }

        $data = json_decode(wp_remote_retrieve_body($response), true);
        if (!isset($data['content']) || !isset($data['encoding'])) {
            return '';
        }

        // GitHub returns base64 encoded content
        if ($data['encoding'] === 'base64') {
            return base64_decode($data['content']);
        }

        return '';
    }

    /**
     * AJAX handler for searching GitHub plugins
     *
     * Handles AJAX requests to search for plugins on GitHub. Implements
     * security measures including CSRF protection, rate limiting, input
     * validation, and secure response handling.
     *
     * This method is hooked to the 'wp_ajax_search_github_plugins' action
     * and is only accessible to authenticated users with the 'install_plugins'
     * capability.
     *
     * @since 1.0.0
     */
    public function ajax_search_plugins()
    {
        // Get CSRF protection instance
        $csrf_protection = $this->parent->get_csrf_protection();

        // Verify request with enhanced security
        if (!$csrf_protection->verify_ajax_request('nonce', 'bulk_update_q_plugins', 'install_plugins')) {
            wp_send_json_error([
                'message' => __('Security check failed. Please refresh the page and try again.', 'q-updater'),
                'code' => 'invalid_nonce'
            ], 403);
            exit;
        }

        // Additional security headers
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');

        // Rate limiting - prevent abuse
        $user_id = get_current_user_id();
        $rate_key = 'q_updater_search_rate_' . $user_id;
        $rate_data = get_transient($rate_key);

        if ($rate_data) {
            $count = $rate_data['count'];
            $time = $rate_data['time'];

            // Limit to 10 requests per minute
            if ($count >= 10 && (time() - $time) < 60) {
                wp_send_json_error(__('Rate limit exceeded. Please try again in a minute.', 'q-updater'));
            }

            // Update count
            set_transient($rate_key, [
                'count' => $count + 1,
                'time' => $time
            ], 60);
        } else {
            // First request
            set_transient($rate_key, [
                'count' => 1,
                'time' => time()
            ], 60);
        }

        // Validate and sanitize input
        $query = isset($_POST['query']) ? sanitize_text_field($_POST['query']) : '';

        // Get repository type
        $repo_type = isset($_POST['repo_type']) ? sanitize_text_field($_POST['repo_type']) : 'github';

        // Validate repository type
        $allowed_repo_types = ['github', 'gitlab', 'bitbucket', 'all'];
        if (!in_array($repo_type, $allowed_repo_types)) {
            $repo_type = 'github'; // Default to GitHub if invalid
        }

        // Validate sort parameter
        $allowed_sort_values = ['stars', 'forks', 'updated', 'help-wanted-issues'];
        $sort = isset($_POST['sort']) && in_array($_POST['sort'], $allowed_sort_values)
            ? $_POST['sort']
            : 'stars';

        // Validate order parameter
        $allowed_order_values = ['desc', 'asc'];
        $order = isset($_POST['order']) && in_array($_POST['order'], $allowed_order_values)
            ? $_POST['order']
            : 'desc';

        if (empty($query)) {
            wp_send_json_error(__('Search query is required', 'q-updater'));
        }

        // Prevent overly long queries
        if (strlen($query) > 100) {
            wp_send_json_error(__('Search query is too long. Maximum 100 characters allowed.', 'q-updater'));
        }

        // Search based on repository type
        $results = [];

        if ($repo_type === 'all' || $repo_type === 'github') {
            $github_results = $this->search_plugins($query, $sort, $order);
            if (is_wp_error($github_results)) {
                error_log('Q-Updater: GitHub search error: ' . $github_results->get_error_message());
            } else {
                $results = array_merge($results, $github_results);
            }
        }

        if ($repo_type === 'all' || $repo_type === 'gitlab') {
            $gitlab_results = $this->search_gitlab_plugins($query, $sort, $order);
            if (is_wp_error($gitlab_results)) {
                error_log('Q-Updater: GitLab search error: ' . $gitlab_results->get_error_message());
            } else {
                $results = array_merge($results, $gitlab_results);
            }
        }

        if ($repo_type === 'all' || $repo_type === 'bitbucket') {
            $bitbucket_results = $this->search_bitbucket_plugins($query, $sort, $order);
            if (is_wp_error($bitbucket_results)) {
                error_log('Q-Updater: Bitbucket search error: ' . $bitbucket_results->get_error_message());
            } else {
                $results = array_merge($results, $bitbucket_results);
            }
        }

        // Sort combined results if needed
        if ($repo_type === 'all') {
            // Sort by the requested criteria
            usort($results, function ($a, $b) use ($sort, $order) {
                $multiplier = ($order === 'desc') ? -1 : 1;

                if ($sort === 'stars') {
                    return $multiplier * ($a['stars'] - $b['stars']);
                } else if ($sort === 'forks') {
                    return $multiplier * ($a['forks'] - $b['forks']);
                } else if ($sort === 'updated') {
                    return $multiplier * strtotime($a['updated_at']) - strtotime($b['updated_at']);
                }

                // Default to stars
                return $multiplier * ($a['stars'] - $b['stars']);
            });
        }

        wp_send_json_success($results);
    }

    /**
     * Search GitLab plugins
     *
     * @param string $query Search query
     * @param string $sort Sort field (stars, forks, updated)
     * @param string $order Sort order (desc, asc)
     * @return array|WP_Error Search results or error object on failure
     */
    private function search_gitlab_plugins($query, $sort = 'stars', $order = 'desc')
    {
        // Map sort parameters to GitLab API parameters
        $gitlab_sort = 'stars';
        if ($sort === 'updated') {
            $gitlab_sort = 'updated_at';
        } else if ($sort === 'forks') {
            $gitlab_sort = 'forks_count';
        }

        // Add 'wordpress plugin' to the query to improve results
        $search_query = urlencode($query . ' wordpress plugin');
        $url = "https://gitlab.com/api/v4/projects?search={$search_query}&order_by={$gitlab_sort}&sort={$order}&per_page=10";

        $headers = $this->get_gitlab_headers();

        $response = wp_remote_get($url, [
            'headers' => $headers,
            'timeout' => 15,
            'sslverify' => true
        ]);

        if (is_wp_error($response)) {
            return new WP_Error(
                'gitlab_api_error',
                sprintf(__('GitLab API Error: %s', 'q-updater'), $response->get_error_message())
            );
        }

        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            return new WP_Error(
                'gitlab_api_error',
                sprintf(__('GitLab API Error: HTTP %s', 'q-updater'), $response_code)
            );
        }

        $data = json_decode(wp_remote_retrieve_body($response), true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return new WP_Error(
                'gitlab_api_error',
                __('Invalid JSON response from GitLab API', 'q-updater')
            );
        }

        $results = [];
        foreach ($data as $repo) {
            // Get readme content to extract more plugin information
            $readme_content = '';

            $results[] = [
                'name' => $repo['name'],
                'full_name' => $repo['path_with_namespace'],
                'description' => $repo['description'],
                'stars' => $repo['star_count'],
                'forks' => $repo['forks_count'],
                'updated_at' => $repo['last_activity_at'],
                'html_url' => $repo['web_url'],
                'readme' => $readme_content,
                'source' => 'gitlab',
                'owner' => [
                    'login' => explode('/', $repo['path_with_namespace'])[0],
                    'avatar_url' => $repo['avatar_url'] ?? '',
                    'html_url' => "https://gitlab.com/" . explode('/', $repo['path_with_namespace'])[0]
                ]
            ];
        }

        return $results;
    }

    /**
     * Search Bitbucket plugins
     *
     * @param string $query Search query
     * @param string $sort Sort field (stars, forks, updated)
     * @param string $order Sort order (desc, asc)
     * @return array|WP_Error Search results or error object on failure
     */
    private function search_bitbucket_plugins($query, $sort = 'stars', $order = 'desc')
    {
        // Add 'wordpress plugin' to the query to improve results
        $search_query = urlencode($query . ' wordpress plugin');
        $url = "https://api.bitbucket.org/2.0/repositories?q=name~\"$search_query\"&sort=$sort&pagelen=10";

        $headers = $this->get_bitbucket_headers();

        $response = wp_remote_get($url, [
            'headers' => $headers,
            'timeout' => 15,
            'sslverify' => true
        ]);

        if (is_wp_error($response)) {
            return new WP_Error(
                'bitbucket_api_error',
                sprintf(__('Bitbucket API Error: %s', 'q-updater'), $response->get_error_message())
            );
        }

        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            return new WP_Error(
                'bitbucket_api_error',
                sprintf(__('Bitbucket API Error: HTTP %s', 'q-updater'), $response_code)
            );
        }

        $data = json_decode(wp_remote_retrieve_body($response), true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return new WP_Error(
                'bitbucket_api_error',
                __('Invalid JSON response from Bitbucket API', 'q-updater')
            );
        }

        if (!isset($data['values']) || !is_array($data['values'])) {
            return new WP_Error(
                'bitbucket_api_error',
                __('Invalid response format from Bitbucket API', 'q-updater')
            );
        }

        $results = [];
        foreach ($data['values'] as $repo) {
            // Get readme content to extract more plugin information
            $readme_content = '';

            $results[] = [
                'name' => $repo['name'],
                'full_name' => $repo['full_name'],
                'description' => $repo['description'] ?? '',
                'stars' => $repo['watchers_count'] ?? 0,
                'forks' => $repo['forks_count'] ?? 0,
                'updated_at' => $repo['updated_on'] ?? '',
                'html_url' => $repo['links']['html']['href'] ?? '',
                'readme' => $readme_content,
                'source' => 'bitbucket',
                'owner' => [
                    'login' => $repo['owner']['username'] ?? '',
                    'avatar_url' => $repo['owner']['links']['avatar']['href'] ?? '',
                    'html_url' => $repo['owner']['links']['html']['href'] ?? ''
                ]
            ];
        }

        // Sort results according to the requested sort parameter
        if ($sort === 'stars') {
            usort($results, function ($a, $b) use ($order) {
                return ($order === 'desc') ? $b['stars'] - $a['stars'] : $a['stars'] - $b['stars'];
            });
        } else if ($sort === 'forks') {
            usort($results, function ($a, $b) use ($order) {
                return ($order === 'desc') ? $b['forks'] - $a['forks'] : $a['forks'] - $b['forks'];
            });
        } else if ($sort === 'updated') {
            usort($results, function ($a, $b) use ($order) {
                $time_a = strtotime($a['updated_at']);
                $time_b = strtotime($b['updated_at']);
                return ($order === 'desc') ? $time_b - $time_a : $time_a - $time_b;
            });
        }

        return $results;
    }

    /**
     * Get latest release from GitHub
     *
     * Fetches information about the latest release of a GitHub repository.
     * Results are cached for 6 hours to reduce API calls and improve performance.
     *
     * The method validates the repository name, secures the API request,
     * validates the response, and formats the release information for use
     * in the plugin update process.
     *
     * @since 1.0.0
     * @param string $repo GitHub repository in the format "username/repo"
     * @return array|WP_Error Release information array with 'version' and 'download_url' keys,
     *                        or WP_Error object on failure
     */
    public function get_latest_release($repo)
    {
        // Validate repository name
        if (!$this->security->validate_repository_name($repo)) {
            return new WP_Error(
                'invalid_repo',
                __('Invalid repository format. Use: username/repository', 'q-updater')
            );
        }

        $transient_key = 'q_updater_latest_release_' . str_replace('/', '_', $repo);
        $release = get_transient($transient_key);

        if ($release === false) {
            $url = "https://api.github.com/repos/$repo/releases/latest";

            // Validate and secure the API request
            $validated = $this->security->validate_github_api_request(
                $url,
                $this->get_github_headers()
            );

            $response = wp_remote_get(
                $validated['endpoint'],
                $validated['args']
            );

            if (is_wp_error($response)) {
                return new WP_Error(
                    'github_api_error',
                    sprintf(__('GitHub API Error: %s', 'q-updater'), $response->get_error_message())
                );
            }

            $response_code = wp_remote_retrieve_response_code($response);
            if ($response_code !== 200) {
                return new WP_Error(
                    'github_api_error',
                    sprintf(__('GitHub API Error: HTTP %s', 'q-updater'), $response_code)
                );
            }

            $release = json_decode(wp_remote_retrieve_body($response), true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return new WP_Error(
                    'github_api_error',
                    __('Invalid JSON response from GitHub API', 'q-updater')
                );
            }

            if (empty($release['tag_name']) || empty($release['assets'][0]['browser_download_url'])) {
                return new WP_Error(
                    'github_api_error',
                    __('No valid release data found', 'q-updater')
                );
            }

            set_transient($transient_key, $release, HOUR_IN_SECONDS * 6); // Cache for 6 hours
        }

        return [
            'version' => ltrim($release['tag_name'], 'v'),
            'download_url' => $release['assets'][0]['browser_download_url']
        ];
    }

    /**
     * Get all releases from a repository
     *
     * Fetches information about all releases of a repository (GitHub, GitLab, or Bitbucket).
     * Results are cached for 6 hours to reduce API calls and improve performance.
     *
     * The method validates the repository name, secures the API request,
     * validates the response, and formats the release information for use
     * in the plugin update and rollback processes.
     *
     * Each release in the returned array includes version, download URL,
     * publication date, and release description.
     *
     * @since 1.0.0
     * @param string $repo Repository in the format "username/repo" or with prefix (gitlab:username/repo)
     * @return array|WP_Error Array of formatted release information or WP_Error on failure
     */
    public function get_all_releases($repo)
    {
        // Get repository API info
        $repo_info = $this->get_repository_api_info($repo);
        $repo_type = $repo_info['type'];
        $repo_path = $repo_info['repo_path'];

        // Validate repository name using appropriate method
        if ($repo_type === 'github' && !$this->security->validate_repository_name($repo_path)) {
            error_log("Q-Updater: Invalid repository format: {$repo}");
            return new WP_Error(
                'invalid_repo',
                __('Invalid repository format. Use: username/repository', 'q-updater')
            );
        }

        // Check if we should bypass cache for debugging
        $bypass_cache = isset($_GET['qu_bypass_cache']) || (defined('QU_BYPASS_CACHE') && QU_BYPASS_CACHE);

        $transient_key = $this->parent->get_option_name('releases_transient_prefix') . str_replace(['/', ':', '.'], '_', $repo);
        $releases = $bypass_cache ? false : get_transient($transient_key);

        if ($releases === false) {
            // Get releases based on repository type
            switch ($repo_type) {
                case 'github':
                    $releases = $this->get_github_releases($repo_path);
                    break;
                case 'gitlab':
                    $releases = $this->get_gitlab_releases($repo_path);
                    break;
                case 'bitbucket':
                    $releases = $this->get_bitbucket_releases($repo_path);
                    break;
                default:
                    return new WP_Error(
                        'invalid_repo_type',
                        __('Invalid repository type. Supported types: GitHub, GitLab, Bitbucket.', 'q-updater')
                    );
            }

            // If we got an error, return it
            if (is_wp_error($releases)) {
                return $releases;
            }

            // Cache the releases
            set_transient($transient_key, $releases, HOUR_IN_SECONDS * 6);
        }

        return $releases;
    }

    /**
     * Get releases from GitHub repository
     *
     * @param string $repo Repository in the format "username/repo"
     * @return array|WP_Error Array of formatted release information or WP_Error on failure
     */
    private function get_github_releases($repo)
    {
        $url = "https://api.github.com/repos/$repo/releases";
        error_log("Q-Updater: Fetching releases from GitHub API: {$url}");

        // Get headers with authentication
        $headers = $this->get_github_headers();

        // Log if we have authentication headers (without revealing the token)
        if (isset($headers['Authorization'])) {
            error_log("Q-Updater: Using authenticated GitHub API request");
        } else {
            error_log("Q-Updater: Using unauthenticated GitHub API request - rate limits may apply");
        }

        // Validate and secure the API request
        $validated = $this->security->validate_github_api_request(
            $url,
            $headers
        );

        $response = wp_remote_get(
            $validated['endpoint'],
            $validated['args']
        );

        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            // Log error only if WP_DEBUG is enabled
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("Q-Updater: GitHub API Error when fetching releases: {$error_message}");
            }
            return new WP_Error(
                'github_api_error',
                sprintf(__('GitHub API Error: %s', 'q-updater'), $error_message)
            );
        }

        $response_code = wp_remote_retrieve_response_code($response);

        // Log rate limit information
        $rate_limit = wp_remote_retrieve_header($response, 'x-ratelimit-limit');
        $rate_remaining = wp_remote_retrieve_header($response, 'x-ratelimit-remaining');
        $rate_reset = wp_remote_retrieve_header($response, 'x-ratelimit-reset');

        if ($rate_limit && $rate_remaining && defined('WP_DEBUG') && WP_DEBUG) {
            error_log("Q-Updater: GitHub API Rate Limit: {$rate_remaining}/{$rate_limit} remaining");
        }

        if ($response_code !== 200) {
            $body = wp_remote_retrieve_body($response);
            $error_data = json_decode($body, true);
            $error_message = isset($error_data['message']) ? $error_data['message'] : 'Unknown error';

            error_log("Q-Updater: GitHub API Error HTTP {$response_code}: {$error_message}");

            // Check for specific error types
            if ($response_code === 404) {
                // Enhanced 404 error handling with more detailed information
                $detailed_message = sprintf(__('Repository not found: %s. Please verify the repository exists and is accessible.', 'q-updater'), $repo);

                // Add additional debugging information
                if (isset($error_data['documentation_url'])) {
                    $detailed_message .= ' ' . sprintf(__('GitHub Documentation: %s', 'q-updater'), $error_data['documentation_url']);
                }

                // Check if this is a private repository
                if (strpos($error_message, 'Not Found') !== false) {
                    $detailed_message .= ' ' . __('This may be a private repository that requires authentication or the repository does not exist.', 'q-updater');
                }

                // Log detailed error for debugging only if WP_DEBUG is enabled
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("Q-Updater: GitHub 404 Error Details - URL: {$url}, Error: {$error_message}");
                }

                return new WP_Error(
                    'github_api_error',
                    $detailed_message
                );
            } elseif ($response_code === 401) {
                return new WP_Error(
                    'github_api_error',
                    __('GitHub API authentication failed. Please check your GitHub token in the settings.', 'q-updater')
                );
            } elseif ($response_code === 403 && is_string($error_message) && strpos($error_message, 'rate limit') !== false) {
                $reset_time = $rate_reset ? date('H:i:s', $rate_reset) : 'unknown time';
                return new WP_Error(
                    'github_api_error',
                    sprintf(__('GitHub API rate limit exceeded. Limit will reset at %s. Consider adding a GitHub token in settings.', 'q-updater'), $reset_time)
                );
            } else {
                return new WP_Error(
                    'github_api_error',
                    sprintf(__('GitHub API Error: HTTP %s - %s', 'q-updater'), $response_code, $error_message)
                );
            }
        }

        $releases = json_decode(wp_remote_retrieve_body($response), true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("Q-Updater: Invalid JSON response from GitHub API: " . json_last_error_msg());
            return new WP_Error(
                'github_api_error',
                __('Invalid JSON response from GitHub API', 'q-updater')
            );
        }

        if (!is_array($releases)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("Q-Updater: Invalid response format from GitHub API - expected array but got: " . gettype($releases));
            }
            return new WP_Error(
                'github_api_error',
                __('Invalid response format from GitHub API', 'q-updater')
            );
        }

        $formatted_releases = [];
        foreach ($releases as $release) {
            if (isset($release['tag_name'])) {
                $formatted_releases[] = [
                    'version' => ltrim($release['tag_name'], 'v'),
                    'download_url' => isset($release['assets'][0]['browser_download_url']) ?
                        $release['assets'][0]['browser_download_url'] : '',
                    'published_at' => $release['published_at'],
                    'description' => $release['body'],
                    'source' => 'github'
                ];
            }
        }

        error_log("Q-Updater: Successfully retrieved " . count($formatted_releases) . " releases for {$repo}");
        return $formatted_releases;
    }

    /**
     * Get releases from GitLab repository
     *
     * @param string $repo Repository in the format "username/repo"
     * @return array|WP_Error Array of formatted release information or WP_Error on failure
     */
    private function get_gitlab_releases($repo)
    {
        $url = "https://gitlab.com/api/v4/projects/" . urlencode($repo) . "/releases";
        error_log("Q-Updater: Fetching releases from GitLab API: {$url}");

        // Get headers with authentication
        $headers = $this->get_gitlab_headers();

        $response = wp_remote_get($url, [
            'headers' => $headers,
            'timeout' => 15,
            'sslverify' => true
        ]);

        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            error_log("Q-Updater: GitLab API Error when fetching releases: {$error_message}");
            return new WP_Error(
                'gitlab_api_error',
                sprintf(__('GitLab API Error: %s', 'q-updater'), $error_message)
            );
        }

        $response_code = wp_remote_retrieve_response_code($response);

        if ($response_code !== 200) {
            $body = wp_remote_retrieve_body($response);
            $error_data = json_decode($body, true);
            $error_message = isset($error_data['message']) ? $error_data['message'] : 'Unknown error';

            error_log("Q-Updater: GitLab API Error HTTP {$response_code}: {$error_message}");

            return new WP_Error(
                'gitlab_api_error',
                sprintf(__('GitLab API Error: HTTP %s - %s', 'q-updater'), $response_code, $error_message)
            );
        }

        $releases = json_decode(wp_remote_retrieve_body($response), true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("Q-Updater: Invalid JSON response from GitLab API: " . json_last_error_msg());
            return new WP_Error(
                'gitlab_api_error',
                __('Invalid JSON response from GitLab API', 'q-updater')
            );
        }

        if (!is_array($releases)) {
            error_log("Q-Updater: Invalid response format from GitLab API - expected array but got: " . gettype($releases));
            return new WP_Error(
                'gitlab_api_error',
                __('Invalid response format from GitLab API', 'q-updater')
            );
        }

        $formatted_releases = [];
        foreach ($releases as $release) {
            if (isset($release['tag_name'])) {
                // Get download URL from assets or sources
                $download_url = '';
                if (isset($release['assets']['links']) && is_array($release['assets']['links'])) {
                    foreach ($release['assets']['links'] as $link) {
                        if (isset($link['url']) && isset($link['name']) && strpos($link['name'], '.zip') !== false) {
                            $download_url = $link['url'];
                            break;
                        }
                    }
                }

                // If no download URL found, use the source code URL
                if (empty($download_url) && isset($release['_links']['self'])) {
                    $download_url = $release['_links']['self'] . '/downloads/source.zip';
                }

                $formatted_releases[] = [
                    'version' => ltrim($release['tag_name'], 'v'),
                    'download_url' => $download_url,
                    'published_at' => $release['created_at'],
                    'description' => $release['description'],
                    'source' => 'gitlab'
                ];
            }
        }

        error_log("Q-Updater: Successfully retrieved " . count($formatted_releases) . " releases for {$repo}");
        return $formatted_releases;
    }

    /**
     * Get releases from Bitbucket repository
     *
     * @param string $repo Repository in the format "username/repo"
     * @return array|WP_Error Array of formatted release information or WP_Error on failure
     */
    private function get_bitbucket_releases($repo)
    {
        $url = "https://api.bitbucket.org/2.0/repositories/{$repo}/refs/tags";
        error_log("Q-Updater: Fetching releases from Bitbucket API: {$url}");

        // Get headers with authentication
        $headers = $this->get_bitbucket_headers();

        $response = wp_remote_get($url, [
            'headers' => $headers,
            'timeout' => 15,
            'sslverify' => true
        ]);

        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            error_log("Q-Updater: Bitbucket API Error when fetching releases: {$error_message}");
            return new WP_Error(
                'bitbucket_api_error',
                sprintf(__('Bitbucket API Error: %s', 'q-updater'), $error_message)
            );
        }

        $response_code = wp_remote_retrieve_response_code($response);

        if ($response_code !== 200) {
            $body = wp_remote_retrieve_body($response);
            $error_data = json_decode($body, true);
            $error_message = isset($error_data['error']['message']) ? $error_data['error']['message'] : 'Unknown error';

            error_log("Q-Updater: Bitbucket API Error HTTP {$response_code}: {$error_message}");

            return new WP_Error(
                'bitbucket_api_error',
                sprintf(__('Bitbucket API Error: HTTP %s - %s', 'q-updater'), $response_code, $error_message)
            );
        }

        $tags_data = json_decode(wp_remote_retrieve_body($response), true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("Q-Updater: Invalid JSON response from Bitbucket API: " . json_last_error_msg());
            return new WP_Error(
                'bitbucket_api_error',
                __('Invalid JSON response from Bitbucket API', 'q-updater')
            );
        }

        if (!isset($tags_data['values']) || !is_array($tags_data['values'])) {
            error_log("Q-Updater: Invalid response format from Bitbucket API - expected 'values' array");
            return new WP_Error(
                'bitbucket_api_error',
                __('Invalid response format from Bitbucket API', 'q-updater')
            );
        }

        $formatted_releases = [];
        foreach ($tags_data['values'] as $tag) {
            if (isset($tag['name'])) {
                // Construct download URL for the tag
                $download_url = "https://bitbucket.org/{$repo}/get/{$tag['name']}.zip";

                $formatted_releases[] = [
                    'version' => ltrim($tag['name'], 'v'),
                    'download_url' => $download_url,
                    'published_at' => isset($tag['date']) ? $tag['date'] : '',
                    'description' => isset($tag['message']) ? $tag['message'] : '',
                    'source' => 'bitbucket'
                ];
            }
        }

        error_log("Q-Updater: Successfully retrieved " . count($formatted_releases) . " releases for {$repo}");
        return $formatted_releases;
    }

    /**
     * Get repository download URL
     *
     * Generates a download URL for a specific version of a repository.
     * If no version is specified, the latest release is used.
     *
     * The method validates the repository name and version format, and can
     * return either a direct asset download URL (if available) or a fallback
     * to the repository archive URL.
     *
     * @since 1.0.0
     * @param string $repo Repository in the format "username/repo" or with prefix (gitlab:username/repo)
     * @param string $version Version to download (e.g., "1.2.3") or empty for latest
     * @return string|WP_Error Download URL or WP_Error on failure
     */
    public function get_github_download_url($repo, $version = '')
    {
        // Get repository API info
        $repo_info = $this->get_repository_api_info($repo);
        $repo_type = $repo_info['type'];
        $repo_path = $repo_info['repo_path'];

        // Validate version if provided
        if (!empty($version) && !preg_match('/^[a-zA-Z0-9\.\-_]+$/', $version)) {
            return new WP_Error(
                'invalid_version',
                __('Invalid version format. Use only alphanumeric characters, dots, hyphens, and underscores.', 'q-updater')
            );
        }

        // Check for custom endpoint
        $custom_endpoint = get_option('q_updater_custom_endpoint');
        if (!empty($custom_endpoint)) {
            $endpoint = $this->security->enforce_https($custom_endpoint);
            return $endpoint . "/$repo_path/$version.zip";
        }

        // Get download URL based on repository type
        switch ($repo_type) {
            case 'github':
                return $this->get_github_download_url_internal($repo_path, $version);
            case 'gitlab':
                return $this->get_gitlab_download_url($repo_path, $version);
            case 'bitbucket':
                return $this->get_bitbucket_download_url($repo_path, $version);
            default:
                return new WP_Error(
                    'invalid_repo_type',
                    __('Invalid repository type. Supported types: GitHub, GitLab, Bitbucket.', 'q-updater')
                );
        }
    }

    /**
     * Get GitHub download URL (internal implementation)
     *
     * @param string $repo GitHub repository in the format "username/repo"
     * @param string $version Version to download (e.g., "1.2.3") or empty for latest
     * @return string|WP_Error Download URL or WP_Error on failure
     */
    private function get_github_download_url_internal($repo, $version = '')
    {
        // Validate repository name
        if (!$this->security->validate_repository_name($repo)) {
            return new WP_Error(
                'invalid_repo',
                __('Invalid repository format. Use: username/repository', 'q-updater')
            );
        }

        $headers = $this->get_github_headers();

        if (empty($version)) {
            // Get all releases
            $releases = $this->get_github_releases($repo);

            // Check if releases is a WP_Error
            if (is_wp_error($releases)) {
                return $releases; // Return the error
            }

            // Check if we have any releases
            if (empty($releases)) {
                return new WP_Error('no_release', 'No valid releases found');
            }

            // Return the download URL of the first (latest) release
            return $releases[0]['download_url'];
        }

        // Check if version exists
        $url = "https://api.github.com/repos/$repo/releases/tags/$version";

        // Validate and secure the API request
        $validated = $this->security->validate_github_api_request(
            $url,
            $headers
        );

        $response = wp_remote_get(
            $validated['endpoint'],
            $validated['args']
        );

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $release = json_decode(wp_remote_retrieve_body($response), true);

            if (isset($release['assets'][0]['browser_download_url'])) {
                $download_url = $release['assets'][0]['browser_download_url'];
                return $this->security->enforce_https($download_url);
            }
        }

        // Fallback to branch/tag archive
        $archive_url = "https://github.com/$repo/archive/refs/heads/$version.zip";
        return $this->security->enforce_https($archive_url);
    }

    /**
     * Get GitLab download URL
     *
     * @param string $repo GitLab repository in the format "username/repo"
     * @param string $version Version to download (e.g., "1.2.3") or empty for latest
     * @return string|WP_Error Download URL or WP_Error on failure
     */
    private function get_gitlab_download_url($repo, $version = '')
    {
        if (empty($version)) {
            // Get all releases
            $releases = $this->get_gitlab_releases($repo);

            // Check if releases is a WP_Error
            if (is_wp_error($releases)) {
                return $releases; // Return the error
            }

            // Check if we have any releases
            if (empty($releases)) {
                return new WP_Error('no_release', 'No valid releases found');
            }

            // Return the download URL of the first (latest) release
            return $releases[0]['download_url'];
        }

        // If version is specified, construct the download URL
        $encoded_repo = urlencode($repo);
        $encoded_version = urlencode($version);

        // Try to get the specific release first
        $url = "https://gitlab.com/api/v4/projects/{$encoded_repo}/releases/{$encoded_version}";

        $headers = $this->get_gitlab_headers();

        $response = wp_remote_get($url, [
            'headers' => $headers,
            'timeout' => 15,
            'sslverify' => true
        ]);

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $release = json_decode(wp_remote_retrieve_body($response), true);

            // Check for download links in assets
            if (isset($release['assets']['links']) && is_array($release['assets']['links'])) {
                foreach ($release['assets']['links'] as $link) {
                    if (isset($link['url']) && isset($link['name']) && strpos($link['name'], '.zip') !== false) {
                        return $this->security->enforce_https($link['url']);
                    }
                }
            }
        }

        // Fallback to archive download
        return $this->security->enforce_https("https://gitlab.com/{$repo}/-/archive/{$version}/{$version}.zip");
    }

    /**
     * Get Bitbucket download URL
     *
     * @param string $repo Bitbucket repository in the format "username/repo"
     * @param string $version Version to download (e.g., "1.2.3") or empty for latest
     * @return string|WP_Error Download URL or WP_Error on failure
     */
    private function get_bitbucket_download_url($repo, $version = '')
    {
        if (empty($version)) {
            // Get all releases
            $releases = $this->get_bitbucket_releases($repo);

            // Check if releases is a WP_Error
            if (is_wp_error($releases)) {
                return $releases; // Return the error
            }

            // Check if we have any releases
            if (empty($releases)) {
                return new WP_Error('no_release', 'No valid releases found');
            }

            // Return the download URL of the first (latest) release
            return $releases[0]['download_url'];
        }

        // If version is specified, construct the download URL
        return $this->security->enforce_https("https://bitbucket.org/{$repo}/get/{$version}.zip");
    }

    /**
     * AJAX handler for getting plugin releases
     */
    public function get_plugin_releases()
    {
        // Get CSRF protection instance
        $csrf_protection = $this->parent->get_csrf_protection();

        // Verify request with enhanced security
        if (!$csrf_protection->verify_ajax_request('nonce', 'bulk_update_q_plugins', 'update_plugins')) {
            wp_send_json_error([
                'message' => __('Security check failed. Please refresh the page and try again.', 'q-updater'),
                'code' => 'invalid_nonce'
            ], 403);
            exit;
        }

        // Additional security headers
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');

        // Rate limiting - prevent abuse
        $user_id = get_current_user_id();
        $rate_key = 'q_updater_releases_rate_' . $user_id;
        $rate_data = get_transient($rate_key);

        if ($rate_data) {
            $count = $rate_data['count'];
            $time = $rate_data['time'];

            // Limit to 20 requests per minute
            if ($count >= 20 && (time() - $time) < 60) {
                wp_send_json_error(__('Rate limit exceeded. Please try again in a minute.', 'q-updater'));
            }

            // Update count
            set_transient($rate_key, [
                'count' => $count + 1,
                'time' => $time
            ], 60);
        } else {
            // First request
            set_transient($rate_key, [
                'count' => 1,
                'time' => time()
            ], 60);
        }

        if (empty($_POST['repo'])) {
            wp_send_json_error(__('No repository specified', 'q-updater'));
        }

        $repo = sanitize_text_field($_POST['repo']);

        // Validate repository format
        if (!$this->security->validate_repository_name($repo)) {
            wp_send_json_error(__('Invalid repository format. Use: username/repository', 'q-updater'));
        }

        // Test GitHub token before making API request
        $token_manager = $this->parent->get_token_manager();
        $token_test = $token_manager->test_token();

        if ($token_test['status'] === 'error') {
            error_log('Q-Updater: GitHub token test failed: ' . $token_test['message']);
            wp_send_json_error(sprintf(
                __('GitHub API authentication error: %s Please check your GitHub token in the settings.', 'q-updater'),
                $token_test['message']
            ));
            return;
        }

        $releases = $this->get_all_releases($repo);

        // Check if releases is a WP_Error
        if (is_wp_error($releases)) {
            // Log detailed error information
            error_log('Q-Updater: GitHub API Error when getting releases for ' . $repo . ': ' . $releases->get_error_message());
            wp_send_json_error($releases->get_error_message());
        }

        if (empty($releases)) {
            error_log('Q-Updater: No releases found for repository: ' . $repo);
            wp_send_json_error('No releases found');
        }

        wp_send_json_success($releases);
    }
}
